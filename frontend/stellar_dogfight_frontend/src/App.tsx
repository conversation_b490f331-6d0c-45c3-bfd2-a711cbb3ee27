import { useState } from 'react'
import './App.css'

function App() {
  const [playerName, setPlayerName] = useState('')
  const [sessionId, setSessionId] = useState('')
  const [isJoining, setIsJoining] = useState(false)
  const [hasJoined, setHasJoined] = useState(false)

  const handleJoinBattle = () => {
    if (!playerName.trim()) return
    
    setIsJoining(true)
    
    // Generate a unique session ID (in production this would come from the server)
    const uniqueId = `player_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    setSessionId(uniqueId)
    
    // Simulate server connection
    setTimeout(() => {
      setIsJoining(false)
      setHasJoined(true)
      // In the next phase, this would establish WebSocket connection
    }, 1500)
  }

  if (hasJoined) {
    return (
      <div className="game-container">
        <div className="loading-screen">
          <h2>Welcome to the battlefield, {playerName}!</h2>
          <p>Session ID: {sessionId}</p>
          <p>Connecting to game server...</p>
          <div className="loading-spinner"></div>
          <p className="hint">Game environment loading. WebSocket connection will be established in the next development phase.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="entry-container">
      <div className="stars"></div>
      <div className="twinkling"></div>
      
      <div className="entry-card">
        <h1>STELLAR DOGFIGHT</h1>
        <div className="tagline">Real-time multiplayer space combat</div>
        
        <div className="input-group">
          <label htmlFor="playerName">Pilot Callsign</label>
          <input 
            type="text" 
            id="playerName"
            value={playerName}
            onChange={(e) => setPlayerName(e.target.value)}
            placeholder="Enter your name"
            disabled={isJoining}
          />
        </div>
        
        <button 
          className={`join-button ${isJoining ? 'joining' : ''}`}
          onClick={handleJoinBattle}
          disabled={!playerName.trim() || isJoining}
        >
          {isJoining ? 'Connecting...' : 'Join Battle'}
        </button>
        
        <div className="ship-selection-coming-soon">
          <p>Ship selection coming soon</p>
          <div className="ship-icons">
            <div className="ship-icon">I</div>
            <div className="ship-icon">B</div>
            <div className="ship-icon">S</div>
          </div>
          <div className="ship-labels">
            <span>Interceptor</span>
            <span>Bomber</span>
            <span>Strike Fighter</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default App
