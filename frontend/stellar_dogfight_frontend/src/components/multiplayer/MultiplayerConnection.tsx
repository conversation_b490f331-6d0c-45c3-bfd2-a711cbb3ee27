import React, { useState, useEffect, useRef } from 'react';
import { io, Socket } from 'socket.io-client';
import { Vector3, Euler } from 'three';

// Define types for game state
export interface Player {
  id: string;
  name: string;
  ship_type: 'interceptor' | 'bomber' | 'strikeFighter';
  position: {
    x: number;
    y: number;
    z: number;
  };
  rotation: {
    x: number;
    y: number;
    z: number;
  };
  velocity: {
    x: number;
    y: number;
    z: number;
  };
  health: number;
  energy: number;
  score: number;
  kills: number;
  deaths: number;
  team?: string;
}

export interface Projectile {
  id: string;
  player_id: string;
  weapon_type: 'primary' | 'secondary';
  origin: {
    x: number;
    y: number;
    z: number;
  };
  direction: {
    x: number;
    y: number;
    z: number;
  };
  timestamp: number;
}

export interface KillFeedItem {
  killer: {
    id: string;
    name: string;
  };
  victim: {
    id: string;
    name: string;
  };
  weapon_type: 'primary' | 'secondary';
  timestamp: number;
}

export interface GameRoom {
  id: string;
  type: 'free-for-all' | 'team-deathmatch';
  players: Record<string, Player>;
  teams?: {
    alpha: {
      score: number;
      players: Record<string, Player>;
    };
    omega: {
      score: number;
      players: Record<string, Player>;
    };
  };
}

// Multiplayer connection manager
export function useMultiplayerConnection(serverUrl: string = 'http://localhost:5000') {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [connected, setConnected] = useState(false);
  const [playerId, setPlayerId] = useState<string | null>(null);
  const [playerName, setPlayerName] = useState<string>('');
  const [room, setRoom] = useState<GameRoom | null>(null);
  const [players, setPlayers] = useState<Record<string, Player>>({});
  const [projectiles, setProjectiles] = useState<Projectile[]>([]);
  const [killFeed, setKillFeed] = useState<KillFeedItem[]>([]);
  const [error, setError] = useState<string | null>(null);
  
  // Connect to server
  const connect = () => {
    try {
      const newSocket = io(serverUrl);
      setSocket(newSocket);
      
      newSocket.on('connect', () => {
        setConnected(true);
        setError(null);
        console.log('Connected to game server');
      });
      
      newSocket.on('disconnect', () => {
        setConnected(false);
        console.log('Disconnected from game server');
      });
      
      newSocket.on('error', (data: { message: string }) => {
        setError(data.message);
        console.error('Server error:', data.message);
      });
      
      // Handle player registration
      newSocket.on('registration_complete', (data: { player_id: string, name: string }) => {
        setPlayerId(data.player_id);
        setPlayerName(data.name);
        console.log(`Registered as player ${data.name} (${data.player_id})`);
      });
      
      // Handle room joining
      newSocket.on('room_joined', (data: { 
        room_id: string, 
        player_count: number,
        position: { x: number, y: number, z: number },
        players: Record<string, Player>
      }) => {
        setPlayers(data.players);
        setRoom({
          id: data.room_id,
          type: data.room_id === 'team-deathmatch' ? 'team-deathmatch' : 'free-for-all',
          players: data.players
        });
        console.log(`Joined room ${data.room_id} with ${data.player_count} players`);
      });
      
      // Handle player joining
      newSocket.on('player_joined', (data: { player: Player }) => {
        setPlayers(prev => ({
          ...prev,
          [data.player.id]: data.player
        }));
        console.log(`Player ${data.player.name} joined the game`);
      });
      
      // Handle player disconnecting
      newSocket.on('player_disconnected', (data: { player_id: string }) => {
        setPlayers(prev => {
          const newPlayers = { ...prev };
          delete newPlayers[data.player_id];
          return newPlayers;
        });
        console.log(`Player ${data.player_id} disconnected`);
      });
      
      // Handle player position updates
      newSocket.on('player_position', (data: { 
        player_id: string, 
        position: { x: number, y: number, z: number },
        rotation: { x: number, y: number, z: number },
        velocity: { x: number, y: number, z: number }
      }) => {
        setPlayers(prev => {
          if (!prev[data.player_id]) return prev;
          
          return {
            ...prev,
            [data.player_id]: {
              ...prev[data.player_id],
              position: data.position,
              rotation: data.rotation,
              velocity: data.velocity
            }
          };
        });
      });
      
      // Handle projectile firing
      newSocket.on('projectile_fired', (data: Projectile) => {
        setProjectiles(prev => [...prev, data]);
        
        // Remove projectile after a time (would be handled by hit detection in production)
        setTimeout(() => {
          setProjectiles(prev => prev.filter(p => p.id !== data.id));
        }, data.weapon_type === 'primary' ? 2000 : 5000);
      });
      
      // Handle player hit
      newSocket.on('player_hit', (data: {
        target_id: string,
        shooter_id: string,
        damage: number,
        health_remaining: number,
        weapon_type: 'primary' | 'secondary'
      }) => {
        setPlayers(prev => {
          if (!prev[data.target_id]) return prev;
          
          return {
            ...prev,
            [data.target_id]: {
              ...prev[data.target_id],
              health: data.health_remaining
            }
          };
        });
        
        console.log(`Player ${data.target_id} hit by ${data.shooter_id} for ${data.damage} damage`);
      });
      
      // Handle kill feed
      newSocket.on('kill_feed', (data: KillFeedItem) => {
        setKillFeed(prev => [
          {
            ...data,
            timestamp: Date.now()
          },
          ...prev
        ].slice(0, 10)); // Keep only the 10 most recent kills
        
        console.log(`${data.killer.name} killed ${data.victim.name} with ${data.weapon_type}`);
        
        // Update player scores
        setPlayers(prev => {
          const newPlayers = { ...prev };
          
          if (newPlayers[data.killer.id]) {
            newPlayers[data.killer.id] = {
              ...newPlayers[data.killer.id],
              kills: (newPlayers[data.killer.id].kills || 0) + 1,
              score: (newPlayers[data.killer.id].score || 0) + 100
            };
          }
          
          if (newPlayers[data.victim.id]) {
            newPlayers[data.victim.id] = {
              ...newPlayers[data.victim.id],
              deaths: (newPlayers[data.victim.id].deaths || 0) + 1
            };
          }
          
          return newPlayers;
        });
      });
      
      // Handle ability use
      newSocket.on('ability_used', (data: {
        player_id: string,
        ability_type: 'boost' | 'barrel_roll',
        energy_remaining: number
      }) => {
        setPlayers(prev => {
          if (!prev[data.player_id]) return prev;
          
          return {
            ...prev,
            [data.player_id]: {
              ...prev[data.player_id],
              energy: data.energy_remaining
            }
          };
        });
      });
      
      return () => {
        newSocket.disconnect();
      };
    } catch (err) {
      console.error('Failed to connect to server:', err);
      setError('Failed to connect to game server');
    }
  };
  
  // Register player
  const registerPlayer = (name: string) => {
    if (!socket || !connected) {
      setError('Not connected to server');
      return;
    }
    
    socket.emit('register_player', { name });
  };
  
  // Join game room
  const joinRoom = (roomId: string, shipType: 'interceptor' | 'bomber' | 'strikeFighter') => {
    if (!socket || !connected || !playerId) {
      setError('Not connected or not registered');
      return;
    }
    
    socket.emit('join_room', {
      player_id: playerId,
      room_id: roomId,
      ship_type: shipType
    });
  };
  
  // Send position update
  const updatePosition = (position: Vector3, rotation: Euler, velocity: Vector3) => {
    if (!socket || !connected || !playerId || !room) return;
    
    socket.emit('position_update', {
      player_id: playerId,
      position: {
        x: position.x,
        y: position.y,
        z: position.z
      },
      rotation: {
        x: rotation.x,
        y: rotation.y,
        z: rotation.z
      },
      velocity: {
        x: velocity.x,
        y: velocity.y,
        z: velocity.z
      }
    });
  };
  
  // Fire weapon
  const fireWeapon = (weaponType: 'primary' | 'secondary', origin: Vector3, direction: Vector3) => {
    if (!socket || !connected || !playerId || !room) return;
    
    const projectileId = `${playerId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    socket.emit('weapon_fire', {
      player_id: playerId,
      weapon_type: weaponType,
      projectile_id: projectileId,
      origin: {
        x: origin.x,
        y: origin.y,
        z: origin.z
      },
      direction: {
        x: direction.x,
        y: direction.y,
        z: direction.z
      }
    });
  };
  
  // Use ability
  const useAbility = (abilityType: 'boost' | 'barrel_roll') => {
    if (!socket || !connected || !playerId || !room) return;
    
    socket.emit('ability_use', {
      player_id: playerId,
      ability_type: abilityType
    });
  };
  
  // Report hit detection (in a real game, this would be server-side)
  const reportHit = (targetId: string, weaponType: 'primary' | 'secondary') => {
    if (!socket || !connected || !playerId || !room) return;
    
    socket.emit('hit_detection', {
      shooter_id: playerId,
      target_id: targetId,
      weapon_type: weaponType,
      damage: weaponType === 'primary' ? 10 : 25
    });
  };
  
  return {
    connect,
    registerPlayer,
    joinRoom,
    updatePosition,
    fireWeapon,
    useAbility,
    reportHit,
    connected,
    playerId,
    playerName,
    room,
    players,
    projectiles,
    killFeed,
    error
  };
}
