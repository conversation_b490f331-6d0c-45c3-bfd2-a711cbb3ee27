import React, { useRef } from 'react';
import { useFrame } from '@react-three/fiber';
import { Stars, useTexture } from '@react-three/drei';
import { Vector3, Mesh, SphereGeometry, MeshStandardMaterial, Color, Group } from 'three';
import * as THREE from 'three';

// Procedural Space Environment Component
export function SpaceEnvironment() {
  return (
    <>
      {/* Background stars with customized appearance */}
      <Stars 
        radius={100} 
        depth={50} 
        count={5000} 
        factor={4} 
        saturation={0.5} 
        fade 
        speed={0.5}
      />
      
      {/* Distant nebulae as visual landmarks */}
      <Nebula 
        position={[50, 20, -100]} 
        scale={30} 
        color="#4400ff" 
        secondaryColor="#ff00aa"
      />
      <Nebula 
        position={[-80, -30, -120]} 
        scale={40} 
        color="#00aaff" 
        secondaryColor="#88ff00"
      />
      
      {/* Planets with unique textures and atmospheres */}
      <Planet 
        position={[100, 0, -50]} 
        radius={15} 
        textureType="earth" 
        hasAtmosphere={true}
        rotationSpeed={0.0005}
      />
      <Planet 
        position={[-120, 30, -80]} 
        radius={25} 
        textureType="gas" 
        hasAtmosphere={true}
        rotationSpeed={0.0003}
        atmosphereColor="#ffaa00"
      />
      <Planet 
        position={[0, -80, -150]} 
        radius={10} 
        textureType="rocky" 
        hasAtmosphere={false}
        rotationSpeed={0.0007}
      />
      
      {/* Black hole with gravitational effect */}
      <BlackHole position={[-50, 20, -100]} radius={8} />
      
      {/* Asteroid field for cover and obstacles */}
      <AsteroidField 
        position={[0, 0, 0]} 
        count={100} 
        radius={80} 
        minSize={1} 
        maxSize={5}
      />
    </>
  );
}

// Planet component with atmosphere
interface PlanetProps {
  position: [number, number, number];
  radius: number;
  textureType: 'earth' | 'gas' | 'rocky';
  hasAtmosphere: boolean;
  rotationSpeed: number;
  atmosphereColor?: string;
}

function Planet({ 
  position, 
  radius, 
  textureType, 
  hasAtmosphere, 
  rotationSpeed,
  atmosphereColor = '#4488ff' 
}: PlanetProps) {
  const planetRef = useRef<Mesh>(null);
  const atmosphereRef = useRef<Mesh>(null);
  
  // Load appropriate texture based on planet type
  const texturePath = textureType === 'earth' 
    ? '/textures/earth.jpg' 
    : textureType === 'gas' 
      ? '/textures/gas_giant.jpg' 
      : '/textures/rocky_planet.jpg';
  
  // Placeholder texture for development (would use actual textures in production)
  const texture = useTexture({
    map: texturePath
  });
  
  // Rotate planet on each frame
  useFrame(() => {
    if (planetRef.current) {
      planetRef.current.rotation.y += rotationSpeed;
    }
    if (atmosphereRef.current) {
      atmosphereRef.current.rotation.y += rotationSpeed * 0.8;
    }
  });
  
  return (
    <group position={position}>
      {/* Planet body */}
      <mesh ref={planetRef} castShadow receiveShadow>
        <sphereGeometry args={[radius, 64, 64]} />
        <meshStandardMaterial 
          color={textureType === 'earth' ? '#3366aa' : textureType === 'gas' ? '#ffaa44' : '#aa8866'} 
          roughness={textureType === 'rocky' ? 0.8 : 0.4}
          metalness={textureType === 'rocky' ? 0.2 : 0.0}
        />
      </mesh>
      
      {/* Atmosphere layer if enabled */}
      {hasAtmosphere && (
        <mesh ref={atmosphereRef} scale={1.05}>
          <sphereGeometry args={[radius, 32, 32]} />
          <meshStandardMaterial 
            color={atmosphereColor} 
            transparent={true} 
            opacity={0.3} 
            side={THREE.BackSide}
            emissive={atmosphereColor}
            emissiveIntensity={0.2}
          />
        </mesh>
      )}
    </group>
  );
}

// Nebula component for visual landmarks
interface NebulaProps {
  position: [number, number, number];
  scale: number;
  color: string;
  secondaryColor: string;
}

function Nebula({ position, scale, color, secondaryColor }: NebulaProps) {
  const nebulaRef = useRef<Group>(null);
  
  // Slowly rotate nebula for visual effect
  useFrame(({ clock }) => {
    if (nebulaRef.current) {
      nebulaRef.current.rotation.z = Math.sin(clock.getElapsedTime() * 0.05) * 0.1;
      nebulaRef.current.rotation.x = Math.cos(clock.getElapsedTime() * 0.05) * 0.1;
    }
  });
  
  return (
    <group ref={nebulaRef} position={position} scale={[scale, scale, scale]}>
      {/* Create multiple overlapping cloud-like shapes for nebula effect */}
      {Array.from({ length: 15 }).map((_, i) => (
        <mesh 
          key={i} 
          position={[
            Math.sin(i * 0.5) * 2, 
            Math.cos(i * 0.3) * 2, 
            Math.sin(i * 0.7) * 2
          ]}
          scale={[
            0.5 + Math.random() * 1.5,
            0.5 + Math.random() * 1.5,
            0.5 + Math.random() * 1.5
          ]}
        >
          <sphereGeometry args={[1, 8, 8]} />
          <meshStandardMaterial 
            color={i % 2 === 0 ? color : secondaryColor} 
            transparent={true} 
            opacity={0.2 + Math.random() * 0.1}
            emissive={i % 2 === 0 ? color : secondaryColor}
            emissiveIntensity={0.5}
          />
        </mesh>
      ))}
    </group>
  );
}

// Black hole with gravitational effect
interface BlackHoleProps {
  position: [number, number, number];
  radius: number;
}

function BlackHole({ position, radius }: BlackHoleProps) {
  const blackHoleRef = useRef<Group>(null);
  const accretionDiskRef = useRef<Mesh>(null);
  
  // Rotate accretion disk and apply visual effects
  useFrame(({ clock }) => {
    if (accretionDiskRef.current) {
      accretionDiskRef.current.rotation.z += 0.002;
      
      // Pulsating effect
      const scale = 1 + Math.sin(clock.getElapsedTime() * 2) * 0.05;
      accretionDiskRef.current.scale.set(scale, scale, 1);
    }
    
    // In a full implementation, we would apply gravitational effects to nearby objects here
  });
  
  return (
    <group ref={blackHoleRef} position={position}>
      {/* Black hole center */}
      <mesh>
        <sphereGeometry args={[radius * 0.5, 32, 32]} />
        <meshStandardMaterial 
          color="black" 
          emissive="black"
          transparent={true}
          opacity={0.9}
        />
      </mesh>
      
      {/* Event horizon */}
      <mesh>
        <sphereGeometry args={[radius * 0.8, 32, 32]} />
        <meshStandardMaterial 
          color="#000022" 
          transparent={true} 
          opacity={0.7}
          side={THREE.DoubleSide}
        />
      </mesh>
      
      {/* Accretion disk */}
      <mesh ref={accretionDiskRef} rotation={[Math.PI / 2, 0, 0]}>
        <ringGeometry args={[radius, radius * 2.5, 64]} />
        <meshStandardMaterial 
          color="#ff3300" 
          emissive="#ff6600"
          emissiveIntensity={2}
          side={THREE.DoubleSide}
          transparent={true}
          opacity={0.7}
        />
      </mesh>
      
      {/* Light effect */}
      <pointLight color="#ff6600" intensity={5} distance={radius * 10} />
    </group>
  );
}

// Asteroid field for cover and obstacles
interface AsteroidFieldProps {
  position: [number, number, number];
  count: number;
  radius: number;
  minSize: number;
  maxSize: number;
}

function AsteroidField({ position, count, radius, minSize, maxSize }: AsteroidFieldProps) {
  const groupRef = useRef<Group>(null);
  const asteroids = useRef<Array<{
    position: Vector3;
    rotation: Vector3;
    size: number;
    speed: Vector3;
    rotationSpeed: Vector3;
  }>>([]);
  
  // Initialize asteroid field
  React.useEffect(() => {
    asteroids.current = Array.from({ length: count }).map(() => {
      // Random position within a spherical volume
      const theta = Math.random() * Math.PI * 2;
      const phi = Math.acos(2 * Math.random() - 1);
      const r = radius * Math.cbrt(Math.random()); // Cube root for more uniform distribution
      
      const x = r * Math.sin(phi) * Math.cos(theta);
      const y = r * Math.sin(phi) * Math.sin(theta);
      const z = r * Math.cos(phi);
      
      return {
        position: new Vector3(x, y, z),
        rotation: new Vector3(
          Math.random() * Math.PI * 2,
          Math.random() * Math.PI * 2,
          Math.random() * Math.PI * 2
        ),
        size: minSize + Math.random() * (maxSize - minSize),
        speed: new Vector3(
          (Math.random() - 0.5) * 0.01,
          (Math.random() - 0.5) * 0.01,
          (Math.random() - 0.5) * 0.01
        ),
        rotationSpeed: new Vector3(
          (Math.random() - 0.5) * 0.01,
          (Math.random() - 0.5) * 0.01,
          (Math.random() - 0.5) * 0.01
        )
      };
    });
  }, [count, radius, minSize, maxSize]);
  
  // Animate asteroids
  useFrame(() => {
    if (!groupRef.current) return;
    
    // Update each asteroid's position and rotation
    asteroids.current.forEach((asteroid, i) => {
      const child = groupRef.current?.children[i] as Mesh;
      if (!child) return;
      
      // Update position
      asteroid.position.add(asteroid.speed);
      
      // Keep within field radius
      if (asteroid.position.length() > radius) {
        asteroid.position.normalize().multiplyScalar(radius);
        // Bounce off the boundary
        asteroid.speed.negate().multiplyScalar(0.8);
      }
      
      // Update rotation
      asteroid.rotation.x += asteroid.rotationSpeed.x;
      asteroid.rotation.y += asteroid.rotationSpeed.y;
      asteroid.rotation.z += asteroid.rotationSpeed.z;
      
      // Apply to mesh
      child.position.copy(asteroid.position);
      child.rotation.set(asteroid.rotation.x, asteroid.rotation.y, asteroid.rotation.z);
    });
  });
  
  return (
    <group ref={groupRef} position={position}>
      {asteroids.current.map((asteroid, i) => (
        <mesh key={i} position={asteroid.position} castShadow receiveShadow>
          <dodecahedronGeometry args={[asteroid.size, 0]} />
          <meshStandardMaterial 
            color={new Color().setHSL(0.05, 0.1, 0.2 + Math.random() * 0.1)} 
            roughness={0.8 + Math.random() * 0.2}
            metalness={0.1 + Math.random() * 0.3}
          />
        </mesh>
      ))}
    </group>
  );
}
