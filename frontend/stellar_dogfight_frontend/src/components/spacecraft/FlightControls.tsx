import React, { useEffect, useRef } from 'react';
import { use<PERSON>rame, useThree } from '@react-three/fiber';
import { Vector3, Euler, Quaternion, Object3D } from 'three';

// Flight physics constants
const THRUST_FORCE = 0.05;
const MAX_SPEED = 2.0;
const ROLL_RATE = 0.05;
const PITCH_RATE = 0.03;
const YAW_RATE = 0.02;
const DRAG_COEFFICIENT = 0.01;
const BOOST_MULTIPLIER = 2.5;
const ENERGY_REGEN_RATE = 0.2;
const ENERGY_BOOST_COST = 1.0;
const ENERGY_BARREL_ROLL_COST = 15;
const BARREL_ROLL_DURATION = 30; // frames

// Interface for flight control props
interface FlightControlsProps {
  shipRef: React.RefObject<Object3D>;
  shipType: 'interceptor' | 'bomber' | 'strikeFighter';
  onPositionUpdate?: (position: Vector3, rotation: Euler, velocity: Vector3) => void;
  onEnergyChange?: (energy: number) => void;
  onWeaponFire?: (weaponType: 'primary' | 'secondary', origin: Vector3, direction: Vector3) => void;
  onAbilityUse?: (abilityType: 'boost' | 'barrel_roll') => void;
}

// Ship type specific parameters
const shipParameters = {
  interceptor: {
    thrustMultiplier: 1.3,
    maxSpeedMultiplier: 1.4,
    maneuverabilityMultiplier: 1.2,
    energyRegenMultiplier: 1.1,
    mass: 0.8
  },
  bomber: {
    thrustMultiplier: 0.7,
    maxSpeedMultiplier: 0.8,
    maneuverabilityMultiplier: 0.6,
    energyRegenMultiplier: 0.9,
    mass: 1.5
  },
  strikeFighter: {
    thrustMultiplier: 1.0,
    maxSpeedMultiplier: 1.0,
    maneuverabilityMultiplier: 1.0,
    energyRegenMultiplier: 1.0,
    mass: 1.0
  }
};

export function FlightControls({
  shipRef,
  shipType,
  onPositionUpdate,
  onEnergyChange,
  onWeaponFire,
  onAbilityUse
}: FlightControlsProps) {
  // Get Three.js camera and mouse position
  const { camera, mouse } = useThree();
  
  // State for flight physics
  const velocity = useRef(new Vector3(0, 0, 0));
  const acceleration = useRef(new Vector3(0, 0, 0));
  const targetRotation = useRef(new Euler(0, 0, 0));
  const energy = useRef(100);
  const isBoostActive = useRef(false);
  const isBarrelRollActive = useRef(false);
  const barrelRollFrame = useRef(0);
  const barrelRollDirection = useRef(1); // 1 for right, -1 for left
  
  // Input state
  const keys = useRef({
    w: false,
    a: false,
    s: false,
    d: false,
    shift: false,
    space: false,
    leftMouse: false,
    rightMouse: false
  });
  
  // Get ship parameters based on type
  const params = shipParameters[shipType];
  
  // Setup keyboard and mouse event listeners
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key.toLowerCase() === 'w') keys.current.w = true;
      if (e.key.toLowerCase() === 'a') keys.current.a = true;
      if (e.key.toLowerCase() === 's') keys.current.s = true;
      if (e.key.toLowerCase() === 'd') keys.current.d = true;
      if (e.key === 'Shift') keys.current.shift = true;
      if (e.key === ' ') {
        keys.current.space = true;
        // Trigger barrel roll if enough energy
        if (energy.current >= ENERGY_BARREL_ROLL_COST && !isBarrelRollActive.current) {
          isBarrelRollActive.current = true;
          barrelRollFrame.current = 0;
          barrelRollDirection.current = keys.current.a ? -1 : keys.current.d ? 1 : 1;
          energy.current -= ENERGY_BARREL_ROLL_COST;
          if (onEnergyChange) onEnergyChange(energy.current);
          if (onAbilityUse) onAbilityUse('barrel_roll');
        }
      }
    };
    
    const handleKeyUp = (e: KeyboardEvent) => {
      if (e.key.toLowerCase() === 'w') keys.current.w = false;
      if (e.key.toLowerCase() === 'a') keys.current.a = false;
      if (e.key.toLowerCase() === 's') keys.current.s = false;
      if (e.key.toLowerCase() === 'd') keys.current.d = false;
      if (e.key === 'Shift') keys.current.shift = false;
      if (e.key === ' ') keys.current.space = false;
    };
    
    const handleMouseDown = (e: MouseEvent) => {
      if (e.button === 0) {
        keys.current.leftMouse = true;
        // Fire primary weapon
        if (shipRef.current && onWeaponFire) {
          const direction = new Vector3(0, 0, 1).applyQuaternion(shipRef.current.quaternion);
          const origin = shipRef.current.position.clone().add(direction.clone().multiplyScalar(2));
          onWeaponFire('primary', origin, direction);
        }
      }
      if (e.button === 2) {
        keys.current.rightMouse = true;
        // Fire secondary weapon (missiles)
        if (shipRef.current && onWeaponFire) {
          const direction = new Vector3(0, 0, 1).applyQuaternion(shipRef.current.quaternion);
          const origin = shipRef.current.position.clone().add(direction.clone().multiplyScalar(1.5));
          onWeaponFire('secondary', origin, direction);
        }
      }
    };
    
    const handleMouseUp = (e: MouseEvent) => {
      if (e.button === 0) keys.current.leftMouse = false;
      if (e.button === 2) keys.current.rightMouse = false;
    };
    
    // Prevent context menu on right-click
    const handleContextMenu = (e: Event) => {
      e.preventDefault();
    };
    
    // Add event listeners
    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);
    window.addEventListener('mousedown', handleMouseDown);
    window.addEventListener('mouseup', handleMouseUp);
    window.addEventListener('contextmenu', handleContextMenu);
    
    // Cleanup event listeners
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
      window.removeEventListener('mousedown', handleMouseDown);
      window.removeEventListener('mouseup', handleMouseUp);
      window.removeEventListener('contextmenu', handleContextMenu);
    };
  }, [shipRef, onWeaponFire, onAbilityUse]);
  
  // Main flight physics update loop
  useFrame(() => {
    if (!shipRef.current) return;
    
    // Update boost state
    isBoostActive.current = keys.current.shift && energy.current > 0;
    
    // Energy management
    if (isBoostActive.current) {
      energy.current = Math.max(0, energy.current - ENERGY_BOOST_COST);
      if (onAbilityUse && energy.current > 0) onAbilityUse('boost');
    } else {
      energy.current = Math.min(100, energy.current + ENERGY_REGEN_RATE * params.energyRegenMultiplier);
    }
    
    if (onEnergyChange) onEnergyChange(energy.current);
    
    // Calculate thrust based on input
    const thrust = new Vector3(0, 0, 0);
    
    if (keys.current.w) thrust.z += THRUST_FORCE * params.thrustMultiplier;
    if (keys.current.s) thrust.z -= THRUST_FORCE * params.thrustMultiplier * 0.5; // Braking is less powerful
    
    // Apply boost multiplier if active
    if (isBoostActive.current) {
      thrust.multiplyScalar(BOOST_MULTIPLIER);
    }
    
    // Transform thrust direction based on ship's orientation
    const worldThrust = thrust.clone().applyQuaternion(shipRef.current.quaternion);
    
    // Apply thrust to acceleration
    acceleration.current.copy(worldThrust);
    
    // Apply drag force (proportional to velocity squared and in opposite direction)
    const drag = velocity.current.clone().normalize().multiplyScalar(
      -velocity.current.lengthSq() * DRAG_COEFFICIENT * params.mass
    );
    
    acceleration.current.add(drag);
    
    // Update velocity
    velocity.current.add(acceleration.current);
    
    // Limit maximum speed
    const maxSpeed = MAX_SPEED * params.maxSpeedMultiplier * (isBoostActive.current ? BOOST_MULTIPLIER : 1);
    if (velocity.current.length() > maxSpeed) {
      velocity.current.normalize().multiplyScalar(maxSpeed);
    }
    
    // Update position
    shipRef.current.position.add(velocity.current);
    
    // Handle rotation based on mouse position and keyboard input
    // Mouse controls pitch and yaw
    targetRotation.current.y = -mouse.x * Math.PI * 0.5;
    targetRotation.current.x = mouse.y * Math.PI * 0.3;
    
    // Keyboard controls roll
    if (keys.current.a) {
      targetRotation.current.z += ROLL_RATE * params.maneuverabilityMultiplier;
    }
    if (keys.current.d) {
      targetRotation.current.z -= ROLL_RATE * params.maneuverabilityMultiplier;
    }
    
    // Handle barrel roll animation
    if (isBarrelRollActive.current) {
      barrelRollFrame.current++;
      
      // Calculate roll angle based on animation progress
      const rollProgress = barrelRollFrame.current / BARREL_ROLL_DURATION;
      const rollAngle = barrelRollDirection.current * Math.PI * 2 * rollProgress;
      
      // Apply barrel roll rotation
      shipRef.current.rotation.z = rollAngle;
      
      // End barrel roll when animation completes
      if (barrelRollFrame.current >= BARREL_ROLL_DURATION) {
        isBarrelRollActive.current = false;
        shipRef.current.rotation.z = 0;
      }
    } else {
      // Smoothly interpolate to target rotation when not barrel rolling
      shipRef.current.rotation.x += (targetRotation.current.x - shipRef.current.rotation.x) * PITCH_RATE * params.maneuverabilityMultiplier;
      shipRef.current.rotation.y += (targetRotation.current.y - shipRef.current.rotation.y) * YAW_RATE * params.maneuverabilityMultiplier;
      shipRef.current.rotation.z += (targetRotation.current.z - shipRef.current.rotation.z) * ROLL_RATE * params.maneuverabilityMultiplier;
    }
    
    // Update camera position to follow ship
    camera.position.copy(shipRef.current.position);
    camera.position.add(new Vector3(0, 2, -8).applyQuaternion(shipRef.current.quaternion));
    camera.lookAt(shipRef.current.position.clone().add(new Vector3(0, 0, 5).applyQuaternion(shipRef.current.quaternion)));
    
    // Send position updates to server
    if (onPositionUpdate) {
      onPositionUpdate(
        shipRef.current.position.clone(),
        new Euler().copy(shipRef.current.rotation),
        velocity.current.clone()
      );
    }
  });
  
  return null;
}
