import React from 'react';
import { useGLTF } from '@react-three/drei';
import { Mesh, MeshStandardMaterial } from 'three';
import { useFrame } from '@react-three/fiber';

// Spacecraft base class
interface SpacecraftProps {
  position: [number, number, number];
  rotation: [number, number, number];
  scale?: number;
  color?: string;
  isPlayer?: boolean;
}

// Interceptor - Fast and agile, inspired by modern jet fighters like F-22 Raptor
export function Interceptor({ 
  position, 
  rotation, 
  scale = 1, 
  color = '#8a8a8a',
  isPlayer = false 
}: SpacecraftProps) {
  const meshRef = React.useRef<Mesh>(null);
  
  // Engine glow effect for player's ship
  useFrame((state) => {
    if (isPlayer && meshRef.current) {
      const engineGlow = meshRef.current.children.find(child => 
        child.name === 'engine_glow'
      ) as Mesh;
      
      if (engineGlow) {
        const material = engineGlow.material as MeshStandardMaterial;
        material.emissiveIntensity = 0.5 + Math.sin(state.clock.elapsedTime * 5) * 0.3;
      }
    }
  });

  return (
    <group position={position} rotation={rotation} scale={[scale, scale, scale]}>
      <mesh ref={meshRef} castShadow receiveShadow>
        {/* Main fuselage */}
        <mesh position={[0, 0, 0]} castShadow>
          <meshStandardMaterial color={color} roughness={0.4} metalness={0.8} />
          <cylinderGeometry args={[0.4, 0.2, 4, 8]} />
        </mesh>
        
        {/* Cockpit */}
        <mesh position={[0, 0.3, 1.2]} castShadow>
          <meshStandardMaterial color="#2a2a2a" roughness={0.1} metalness={0.9} />
          <sphereGeometry args={[0.4, 16, 16, 0, Math.PI * 2, 0, Math.PI / 2]} />
        </mesh>
        
        {/* Wings */}
        <mesh position={[0, 0, 0]} rotation={[0, 0, 0]} castShadow>
          <meshStandardMaterial color={color} roughness={0.4} metalness={0.7} />
          <boxGeometry args={[3, 0.1, 1.2]} />
        </mesh>
        
        {/* Tail fins */}
        <mesh position={[0, 0.5, -1.5]} rotation={[0.2, 0, 0]} castShadow>
          <meshStandardMaterial color={color} roughness={0.4} metalness={0.7} />
          <boxGeometry args={[0.1, 0.8, 0.8]} />
        </mesh>
        
        {/* Engine nozzles */}
        <mesh position={[0, 0, -2]} rotation={[0, 0, 0]} castShadow>
          <meshStandardMaterial color="#333" roughness={0.2} metalness={0.9} />
          <cylinderGeometry args={[0.3, 0.4, 0.5, 16]} />
        </mesh>
        
        {/* Engine glow */}
        <mesh name="engine_glow" position={[0, 0, -2.3]} rotation={[0, 0, 0]}>
          <meshStandardMaterial 
            color="#00aaff" 
            emissive="#00aaff"
            emissiveIntensity={1} 
            toneMapped={false}
          />
          <cylinderGeometry args={[0.25, 0.1, 0.4, 16]} />
        </mesh>
        
        {/* Weapon hardpoints */}
        <mesh position={[1.2, -0.1, 0.5]} castShadow>
          <meshStandardMaterial color="#222" roughness={0.4} metalness={0.8} />
          <boxGeometry args={[0.2, 0.15, 0.6]} />
        </mesh>
        <mesh position={[-1.2, -0.1, 0.5]} castShadow>
          <meshStandardMaterial color="#222" roughness={0.4} metalness={0.8} />
          <boxGeometry args={[0.2, 0.15, 0.6]} />
        </mesh>
        
        {/* Mechanical details */}
        <mesh position={[0, -0.2, 0.5]} castShadow>
          <meshStandardMaterial color="#444" roughness={0.6} metalness={0.7} />
          <boxGeometry args={[0.8, 0.1, 1.5]} />
        </mesh>
        
        {/* Armor plating */}
        <mesh position={[0, 0.1, 0]} castShadow>
          <meshStandardMaterial color={color} roughness={0.5} metalness={0.6} />
          <boxGeometry args={[0.9, 0.05, 2]} />
        </mesh>
      </mesh>
    </group>
  );
}

// Bomber - Heavy and powerful, inspired by B-2 Spirit and other strategic bombers
export function Bomber({ 
  position, 
  rotation, 
  scale = 1, 
  color = '#3a3a3a',
  isPlayer = false 
}: SpacecraftProps) {
  const meshRef = React.useRef<Mesh>(null);
  
  // Engine glow effect
  useFrame((state) => {
    if (isPlayer && meshRef.current) {
      const engineGlow = meshRef.current.children.find(child => 
        child.name === 'engine_glow'
      ) as Mesh;
      
      if (engineGlow) {
        const material = engineGlow.material as MeshStandardMaterial;
        material.emissiveIntensity = 0.5 + Math.sin(state.clock.elapsedTime * 3) * 0.2;
      }
    }
  });

  return (
    <group position={position} rotation={rotation} scale={[scale, scale, scale]}>
      <mesh ref={meshRef} castShadow receiveShadow>
        {/* Main body - wider and bulkier */}
        <mesh position={[0, 0, 0]} castShadow>
          <meshStandardMaterial color={color} roughness={0.5} metalness={0.7} />
          <boxGeometry args={[2.5, 0.6, 4]} />
        </mesh>
        
        {/* Cockpit */}
        <mesh position={[0, 0.4, 1.5]} castShadow>
          <meshStandardMaterial color="#2a2a2a" roughness={0.1} metalness={0.9} />
          <sphereGeometry args={[0.4, 16, 16, 0, Math.PI * 2, 0, Math.PI / 2]} />
        </mesh>
        
        {/* Wings - wider wingspan */}
        <mesh position={[0, 0, -0.5]} rotation={[0, 0, 0]} castShadow>
          <meshStandardMaterial color={color} roughness={0.5} metalness={0.6} />
          <boxGeometry args={[5, 0.2, 2]} />
        </mesh>
        
        {/* Heavy armor plating */}
        <mesh position={[0, 0.4, 0]} castShadow>
          <meshStandardMaterial color="#555" roughness={0.6} metalness={0.5} />
          <boxGeometry args={[2, 0.1, 3.5]} />
        </mesh>
        
        {/* Bomb bay */}
        <mesh position={[0, -0.4, 0]} castShadow>
          <meshStandardMaterial color="#222" roughness={0.4} metalness={0.8} />
          <boxGeometry args={[1.2, 0.2, 2]} />
        </mesh>
        
        {/* Engine housings */}
        <mesh position={[1.5, 0.1, -1.5]} castShadow>
          <meshStandardMaterial color="#333" roughness={0.3} metalness={0.8} />
          <boxGeometry args={[0.8, 0.5, 1]} />
        </mesh>
        <mesh position={[-1.5, 0.1, -1.5]} castShadow>
          <meshStandardMaterial color="#333" roughness={0.3} metalness={0.8} />
          <boxGeometry args={[0.8, 0.5, 1]} />
        </mesh>
        
        {/* Engine nozzles */}
        <mesh position={[1.5, 0.1, -2]} castShadow>
          <meshStandardMaterial color="#222" roughness={0.2} metalness={0.9} />
          <cylinderGeometry args={[0.3, 0.4, 0.5, 16]} />
        </mesh>
        <mesh position={[-1.5, 0.1, -2]} castShadow>
          <meshStandardMaterial color="#222" roughness={0.2} metalness={0.9} />
          <cylinderGeometry args={[0.3, 0.4, 0.5, 16]} />
        </mesh>
        
        {/* Engine glow */}
        <mesh name="engine_glow" position={[1.5, 0.1, -2.3]}>
          <meshStandardMaterial 
            color="#ff3300" 
            emissive="#ff3300"
            emissiveIntensity={1} 
            toneMapped={false}
          />
          <cylinderGeometry args={[0.25, 0.1, 0.4, 16]} />
        </mesh>
        <mesh name="engine_glow" position={[-1.5, 0.1, -2.3]}>
          <meshStandardMaterial 
            color="#ff3300" 
            emissive="#ff3300"
            emissiveIntensity={1} 
            toneMapped={false}
          />
          <cylinderGeometry args={[0.25, 0.1, 0.4, 16]} />
        </mesh>
        
        {/* Mechanical details - hydraulics, vents, etc. */}
        <mesh position={[0.8, 0.3, 0.5]} castShadow>
          <meshStandardMaterial color="#444" roughness={0.7} metalness={0.6} />
          <cylinderGeometry args={[0.1, 0.1, 0.8, 8]} />
        </mesh>
        <mesh position={[-0.8, 0.3, 0.5]} castShadow>
          <meshStandardMaterial color="#444" roughness={0.7} metalness={0.6} />
          <cylinderGeometry args={[0.1, 0.1, 0.8, 8]} />
        </mesh>
        
        {/* Weapon hardpoints - missile racks */}
        <mesh position={[1.2, -0.1, 0.5]} castShadow>
          <meshStandardMaterial color="#222" roughness={0.4} metalness={0.8} />
          <boxGeometry args={[0.3, 0.2, 1.2]} />
        </mesh>
        <mesh position={[-1.2, -0.1, 0.5]} castShadow>
          <meshStandardMaterial color="#222" roughness={0.4} metalness={0.8} />
          <boxGeometry args={[0.3, 0.2, 1.2]} />
        </mesh>
      </mesh>
    </group>
  );
}

// Strike Fighter - Balanced, inspired by F/A-18 and other multi-role fighters
export function StrikeFighter({ 
  position, 
  rotation, 
  scale = 1, 
  color = '#5a5a5a',
  isPlayer = false 
}: SpacecraftProps) {
  const meshRef = React.useRef<Mesh>(null);
  
  // Engine glow effect
  useFrame((state) => {
    if (isPlayer && meshRef.current) {
      const engineGlow = meshRef.current.children.find(child => 
        child.name === 'engine_glow'
      ) as Mesh;
      
      if (engineGlow) {
        const material = engineGlow.material as MeshStandardMaterial;
        material.emissiveIntensity = 0.5 + Math.sin(state.clock.elapsedTime * 4) * 0.25;
      }
    }
  });

  return (
    <group position={position} rotation={rotation} scale={[scale, scale, scale]}>
      <mesh ref={meshRef} castShadow receiveShadow>
        {/* Main fuselage - balanced proportions */}
        <mesh position={[0, 0, 0]} castShadow>
          <meshStandardMaterial color={color} roughness={0.4} metalness={0.8} />
          <cylinderGeometry args={[0.5, 0.3, 3.5, 8]} />
        </mesh>
        
        {/* Cockpit */}
        <mesh position={[0, 0.3, 1]} castShadow>
          <meshStandardMaterial color="#2a2a2a" roughness={0.1} metalness={0.9} />
          <sphereGeometry args={[0.4, 16, 16, 0, Math.PI * 2, 0, Math.PI / 2]} />
        </mesh>
        
        {/* Wings - angled for better maneuverability */}
        <mesh position={[0, 0, -0.2]} rotation={[0, 0.2, 0]} castShadow>
          <meshStandardMaterial color={color} roughness={0.4} metalness={0.7} />
          <boxGeometry args={[3.5, 0.15, 1.5]} />
        </mesh>
        
        {/* Tail fins */}
        <mesh position={[0, 0.6, -1.2]} rotation={[0.1, 0, 0]} castShadow>
          <meshStandardMaterial color={color} roughness={0.4} metalness={0.7} />
          <boxGeometry args={[0.15, 0.9, 0.9]} />
        </mesh>
        
        {/* Horizontal stabilizers */}
        <mesh position={[0, 0, -1.5]} rotation={[0, 0, 0]} castShadow>
          <meshStandardMaterial color={color} roughness={0.4} metalness={0.7} />
          <boxGeometry args={[2, 0.1, 0.8]} />
        </mesh>
        
        {/* Engine housings */}
        <mesh position={[0.6, 0, -1.5]} castShadow>
          <meshStandardMaterial color="#333" roughness={0.3} metalness={0.8} />
          <cylinderGeometry args={[0.4, 0.4, 1, 16]} />
        </mesh>
        <mesh position={[-0.6, 0, -1.5]} castShadow>
          <meshStandardMaterial color="#333" roughness={0.3} metalness={0.8} />
          <cylinderGeometry args={[0.4, 0.4, 1, 16]} />
        </mesh>
        
        {/* Engine nozzles */}
        <mesh position={[0.6, 0, -2]} castShadow>
          <meshStandardMaterial color="#222" roughness={0.2} metalness={0.9} />
          <cylinderGeometry args={[0.35, 0.45, 0.5, 16]} />
        </mesh>
        <mesh position={[-0.6, 0, -2]} castShadow>
          <meshStandardMaterial color="#222" roughness={0.2} metalness={0.9} />
          <cylinderGeometry args={[0.35, 0.45, 0.5, 16]} />
        </mesh>
        
        {/* Engine glow */}
        <mesh name="engine_glow" position={[0.6, 0, -2.3]}>
          <meshStandardMaterial 
            color="#66ff00" 
            emissive="#66ff00"
            emissiveIntensity={1} 
            toneMapped={false}
          />
          <cylinderGeometry args={[0.3, 0.1, 0.4, 16]} />
        </mesh>
        <mesh name="engine_glow" position={[-0.6, 0, -2.3]}>
          <meshStandardMaterial 
            color="#66ff00" 
            emissive="#66ff00"
            emissiveIntensity={1} 
            toneMapped={false}
          />
          <cylinderGeometry args={[0.3, 0.1, 0.4, 16]} />
        </mesh>
        
        {/* Weapon hardpoints - balanced loadout */}
        <mesh position={[1.4, -0.1, 0.3]} castShadow>
          <meshStandardMaterial color="#222" roughness={0.4} metalness={0.8} />
          <boxGeometry args={[0.25, 0.2, 0.8]} />
        </mesh>
        <mesh position={[-1.4, -0.1, 0.3]} castShadow>
          <meshStandardMaterial color="#222" roughness={0.4} metalness={0.8} />
          <boxGeometry args={[0.25, 0.2, 0.8]} />
        </mesh>
        <mesh position={[0.8, -0.1, 0.3]} castShadow>
          <meshStandardMaterial color="#222" roughness={0.4} metalness={0.8} />
          <boxGeometry args={[0.2, 0.15, 0.6]} />
        </mesh>
        <mesh position={[-0.8, -0.1, 0.3]} castShadow>
          <meshStandardMaterial color="#222" roughness={0.4} metalness={0.8} />
          <boxGeometry args={[0.2, 0.15, 0.6]} />
        </mesh>
        
        {/* Armor plating and mechanical details */}
        <mesh position={[0, 0.2, 0.2]} castShadow>
          <meshStandardMaterial color="#444" roughness={0.6} metalness={0.7} />
          <boxGeometry args={[1, 0.1, 2]} />
        </mesh>
        <mesh position={[0, -0.2, 0.5]} castShadow>
          <meshStandardMaterial color="#333" roughness={0.5} metalness={0.8} />
          <boxGeometry args={[0.7, 0.1, 1.8]} />
        </mesh>
        
        {/* Additional mechanical details */}
        <mesh position={[0.3, 0.4, -0.5]} rotation={[0, 0, Math.PI/4]} castShadow>
          <meshStandardMaterial color="#555" roughness={0.7} metalness={0.6} />
          <boxGeometry args={[0.1, 0.1, 0.5]} />
        </mesh>
        <mesh position={[-0.3, 0.4, -0.5]} rotation={[0, 0, -Math.PI/4]} castShadow>
          <meshStandardMaterial color="#555" roughness={0.7} metalness={0.6} />
          <boxGeometry args={[0.1, 0.1, 0.5]} />
        </mesh>
      </mesh>
    </group>
  );
}
