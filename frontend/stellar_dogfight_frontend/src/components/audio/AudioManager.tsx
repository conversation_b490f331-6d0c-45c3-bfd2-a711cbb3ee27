import React, { useEffect, useState } from 'react';

// Audio file mapping
const AUDIO_FILES = {
  // Background music
  music_menu: '/audio/music_menu.mp3',
  music_combat: '/audio/music_combat.mp3',
  
  // Weapon sounds
  laser_fire: '/audio/laser_fire.mp3',
  missile_launch: '/audio/missile_launch.mp3',
  missile_lock: '/audio/missile_lock.mp3',
  
  // Ship sounds
  engine_idle: '/audio/engine_idle.mp3',
  engine_boost: '/audio/engine_boost.mp3',
  barrel_roll: '/audio/barrel_roll.mp3',
  
  // Combat sounds
  explosion: '/audio/explosion.mp3',
  shield_hit: '/audio/shield_hit.mp3',
  hull_hit: '/audio/hull_hit.mp3',
  
  // UI sounds
  ui_button: '/audio/ui_button.mp3',
  ui_alert: '/audio/ui_alert.mp3'
};

// Audio categories for volume control
export enum AudioCategory {
  MUSIC = 'music',
  SFX = 'sfx',
  UI = 'ui'
}

// Audio manager hook
export function useAudioManager() {
  // Audio elements cache
  const [audioElements, setAudioElements] = useState<Record<string, HTMLAudioElement>>({});
  
  // Volume settings
  const [volumes, setVolumes] = useState({
    [AudioCategory.MUSIC]: 0.5,
    [AudioCategory.SFX]: 0.8,
    [AudioCategory.UI]: 0.7
  });
  
  // Initialize audio elements
  useEffect(() => {
    const elements: Record<string, HTMLAudioElement> = {};
    
    // Create audio elements for each sound
    Object.entries(AUDIO_FILES).forEach(([key, path]) => {
      const audio = new Audio();
      audio.src = path;
      
      // Set loop for music
      if (key.startsWith('music_')) {
        audio.loop = true;
      }
      
      elements[key] = audio;
    });
    
    setAudioElements(elements);
    
    // Cleanup on unmount
    return () => {
      Object.values(elements).forEach(audio => {
        audio.pause();
        audio.src = '';
      });
    };
  }, []);
  
  // Set volume for a category
  const setVolume = (category: AudioCategory, level: number) => {
    const clampedLevel = Math.max(0, Math.min(1, level));
    
    setVolumes(prev => ({
      ...prev,
      [category]: clampedLevel
    }));
    
    // Apply volume to all audio elements in this category
    Object.entries(audioElements).forEach(([key, audio]) => {
      if (
        (category === AudioCategory.MUSIC && key.startsWith('music_')) ||
        (category === AudioCategory.UI && key.startsWith('ui_')) ||
        (category === AudioCategory.SFX && !key.startsWith('music_') && !key.startsWith('ui_'))
      ) {
        audio.volume = clampedLevel;
      }
    });
  };
  
  // Play a sound
  const playSound = (soundKey: string, volume?: number) => {
    const audio = audioElements[soundKey];
    if (!audio) return;
    
    // Determine category for volume
    let category = AudioCategory.SFX;
    if (soundKey.startsWith('music_')) {
      category = AudioCategory.MUSIC;
    } else if (soundKey.startsWith('ui_')) {
      category = AudioCategory.UI;
    }
    
    // Set volume
    audio.volume = volume !== undefined ? volume : volumes[category];
    
    // Reset and play
    audio.currentTime = 0;
    audio.play().catch(err => console.error('Error playing audio:', err));
  };
  
  // Stop a sound
  const stopSound = (soundKey: string) => {
    const audio = audioElements[soundKey];
    if (!audio) return;
    
    audio.pause();
    audio.currentTime = 0;
  };
  
  // Fade in a sound (useful for music transitions)
  const fadeInSound = (soundKey: string, duration: number = 2000) => {
    const audio = audioElements[soundKey];
    if (!audio) return;
    
    // Determine category for target volume
    let category = AudioCategory.SFX;
    if (soundKey.startsWith('music_')) {
      category = AudioCategory.MUSIC;
    } else if (soundKey.startsWith('ui_')) {
      category = AudioCategory.UI;
    }
    
    const targetVolume = volumes[category];
    
    // Start at zero volume
    audio.volume = 0;
    audio.play().catch(err => console.error('Error playing audio:', err));
    
    // Fade in
    const startTime = Date.now();
    const fadeInterval = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(1, elapsed / duration);
      
      audio.volume = progress * targetVolume;
      
      if (progress >= 1) {
        clearInterval(fadeInterval);
      }
    }, 50);
  };
  
  // Fade out a sound
  const fadeOutSound = (soundKey: string, duration: number = 2000) => {
    const audio = audioElements[soundKey];
    if (!audio) return;
    
    const startVolume = audio.volume;
    const startTime = Date.now();
    
    // Fade out
    const fadeInterval = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(1, elapsed / duration);
      
      audio.volume = startVolume * (1 - progress);
      
      if (progress >= 1) {
        audio.pause();
        audio.currentTime = 0;
        clearInterval(fadeInterval);
      }
    }, 50);
  };
  
  // Play 3D positioned sound (for in-game effects)
  const playPositionalSound = (
    soundKey: string, 
    position: { x: number, y: number, z: number },
    listenerPosition: { x: number, y: number, z: number }
  ) => {
    const audio = audioElements[soundKey];
    if (!audio) return;
    
    // Calculate distance
    const dx = position.x - listenerPosition.x;
    const dy = position.y - listenerPosition.y;
    const dz = position.z - listenerPosition.z;
    const distance = Math.sqrt(dx*dx + dy*dy + dz*dz);
    
    // Maximum distance for audibility
    const maxDistance = 100;
    
    // Calculate volume based on distance (inverse square law)
    const volume = Math.min(1, Math.max(0, 1 - (distance / maxDistance)));
    
    // Calculate stereo pan based on relative x position
    // This is a simple approximation of 3D audio
    const pan = Math.max(-1, Math.min(1, dx / 50));
    
    // Apply volume and play
    if (volume > 0) {
      // In a real implementation, we would use Web Audio API for proper 3D audio
      // For this simplified version, we just adjust volume
      audio.volume = volume * volumes[AudioCategory.SFX];
      audio.currentTime = 0;
      audio.play().catch(err => console.error('Error playing audio:', err));
    }
  };
  
  return {
    playSound,
    stopSound,
    fadeInSound,
    fadeOutSound,
    playPositionalSound,
    setVolume,
    volumes
  };
}
