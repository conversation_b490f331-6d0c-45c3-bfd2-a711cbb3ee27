.hud-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  font-family: 'Arial', sans-serif;
  color: white;
  text-shadow: 0 0 5px rgba(0, 162, 255, 0.7);
  user-select: none;
}

/* Top bar with score and ship info */
.hud-top-bar {
  position: absolute;
  top: 20px;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 40px;
  box-sizing: border-box;
}

.hud-score-container {
  text-align: left;
}

.hud-score {
  font-size: 2.5rem;
  font-weight: bold;
}

.hud-score-label {
  font-size: 0.8rem;
  opacity: 0.7;
}

.hud-ship-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.hud-ship-icon {
  font-size: 1.5rem;
}

.hud-ship-name {
  font-size: 1rem;
  font-weight: bold;
  letter-spacing: 2px;
}

.hud-kd-ratio {
  text-align: right;
}

.hud-kd-stats {
  font-size: 1.5rem;
  font-weight: bold;
}

.hud-kills {
  color: #00ff00;
}

.hud-kd-separator {
  margin: 0 5px;
  opacity: 0.7;
}

.hud-deaths {
  color: #ff0000;
}

.hud-kd-label {
  font-size: 0.8rem;
  opacity: 0.7;
}

/* Bottom bar with health and energy */
.hud-bottom-bar {
  position: absolute;
  bottom: 30px;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  gap: 20px;
}

.hud-stat-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.hud-stat-label {
  font-size: 0.9rem;
  font-weight: bold;
  width: 60px;
  text-align: right;
}

.hud-bar-container {
  width: 200px;
  height: 15px;
  background-color: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  overflow: hidden;
}

.hud-health-bar, .hud-energy-bar {
  height: 100%;
  transition: width 0.3s ease, background-color 0.3s ease;
}

.hud-stat-value {
  width: 50px;
  font-size: 0.9rem;
  font-weight: bold;
}

/* Weapon indicators */
.hud-weapons {
  position: absolute;
  bottom: 100px;
  right: 40px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  align-items: flex-end;
}

.hud-weapon {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  padding: 5px 10px;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 5px;
  border-right: 3px solid;
}

.hud-weapon.primary {
  border-right-color: #ff0000;
}

.hud-weapon.secondary {
  border-right-color: #ffaa00;
}

.hud-weapon-label {
  font-size: 0.7rem;
  opacity: 0.7;
}

.hud-weapon-name {
  font-size: 0.9rem;
  font-weight: bold;
}

.hud-weapon-key {
  font-size: 0.8rem;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 2px 5px;
  border-radius: 3px;
  margin-top: 3px;
}

/* Ability indicators */
.hud-abilities {
  position: absolute;
  bottom: 100px;
  left: 40px;
  display: flex;
  gap: 15px;
}

.hud-ability {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 5px 10px;
  border-radius: 5px;
  border-bottom: 3px solid #00a2ff;
}

.hud-ability-key {
  font-size: 0.8rem;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 2px 5px;
  border-radius: 3px;
  margin-bottom: 3px;
}

.hud-ability-name {
  font-size: 0.9rem;
  font-weight: bold;
}

/* Targeting reticle */
.hud-reticle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.hud-reticle-inner {
  width: 5px;
  height: 5px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  box-shadow: 0 0 5px #00a2ff;
}

.hud-reticle-lines {
  position: absolute;
  width: 100%;
  height: 100%;
}

.hud-reticle-line {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 0 3px #00a2ff;
}

.hud-reticle-line.horizontal {
  width: 100%;
  height: 1px;
  top: 50%;
  left: 0;
}

.hud-reticle-line.vertical {
  width: 1px;
  height: 100%;
  top: 0;
  left: 50%;
}

/* Warning messages */
.hud-warning {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(255, 0, 0, 0.3);
  color: #ff0000;
  padding: 10px 20px;
  border: 1px solid #ff0000;
  border-radius: 5px;
  font-weight: bold;
  animation: warning-flash 1s infinite;
}

.hud-warning.critical {
  animation: warning-flash 0.5s infinite;
}

@keyframes warning-flash {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

/* Connection status */
.connection-status {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  padding: 20px;
  border-radius: 10px;
  text-align: center;
  pointer-events: auto;
}

.connection-status .error {
  color: #ff0000;
  margin-top: 10px;
}

/* Exit button */
.exit-button {
  position: absolute;
  top: 20px;
  right: 20px;
  background-color: rgba(255, 0, 0, 0.7);
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 5px;
  cursor: pointer;
  pointer-events: auto;
  font-weight: bold;
  transition: background-color 0.3s ease;
}

.exit-button:hover {
  background-color: rgba(255, 0, 0, 0.9);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hud-top-bar {
    padding: 0 20px;
  }
  
  .hud-score {
    font-size: 2rem;
  }
  
  .hud-bar-container {
    width: 150px;
  }
  
  .hud-weapons, .hud-abilities {
    bottom: 80px;
  }
}
