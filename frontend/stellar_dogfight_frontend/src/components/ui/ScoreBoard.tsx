import React from 'react';
import { Player } from '../multiplayer/MultiplayerConnection';
import './ScoreBoard.css';

interface ScoreBoardProps {
  players: Player[];
  gameMode: 'free-for-all' | 'team-deathmatch';
}

export function ScoreBoard({ players, gameMode }: ScoreBoardProps) {
  const [isVisible, setIsVisible] = React.useState(false);
  
  // Toggle scoreboard visibility with Tab key
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        e.preventDefault(); // Prevent tab from changing focus
        setIsVisible(true);
      }
    };
    
    const handleKeyUp = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        setIsVisible(false);
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);
    
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, []);
  
  if (!isVisible) return null;
  
  // Sort players by score
  const sortedPlayers = [...players].sort((a, b) => b.score - a.score);
  
  // For team deathmatch, separate players by team
  const teamAlpha = sortedPlayers.filter(p => p.team === 'alpha');
  const teamOmega = sortedPlayers.filter(p => p.team === 'omega');
  const noTeam = sortedPlayers.filter(p => !p.team);
  
  return (
    <div className="scoreboard">
      <div className="scoreboard-header">
        <h2>{gameMode === 'team-deathmatch' ? 'TEAM DEATHMATCH' : 'FREE FOR ALL'}</h2>
        <p className="scoreboard-hint">Hold TAB to view</p>
      </div>
      
      {gameMode === 'team-deathmatch' ? (
        <div className="scoreboard-teams">
          <div className="scoreboard-team alpha">
            <div className="team-header">
              <h3>TEAM ALPHA</h3>
              <div className="team-score">
                {teamAlpha.reduce((sum, p) => sum + p.score, 0)}
              </div>
            </div>
            <table className="scoreboard-table">
              <thead>
                <tr>
                  <th>PILOT</th>
                  <th>SCORE</th>
                  <th>KILLS</th>
                  <th>DEATHS</th>
                  <th>K/D</th>
                </tr>
              </thead>
              <tbody>
                {teamAlpha.map(player => (
                  <tr key={player.id}>
                    <td>{player.name}</td>
                    <td>{player.score}</td>
                    <td>{player.kills}</td>
                    <td>{player.deaths}</td>
                    <td>{player.deaths > 0 ? (player.kills / player.deaths).toFixed(2) : player.kills}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          <div className="scoreboard-team omega">
            <div className="team-header">
              <h3>TEAM OMEGA</h3>
              <div className="team-score">
                {teamOmega.reduce((sum, p) => sum + p.score, 0)}
              </div>
            </div>
            <table className="scoreboard-table">
              <thead>
                <tr>
                  <th>PILOT</th>
                  <th>SCORE</th>
                  <th>KILLS</th>
                  <th>DEATHS</th>
                  <th>K/D</th>
                </tr>
              </thead>
              <tbody>
                {teamOmega.map(player => (
                  <tr key={player.id}>
                    <td>{player.name}</td>
                    <td>{player.score}</td>
                    <td>{player.kills}</td>
                    <td>{player.deaths}</td>
                    <td>{player.deaths > 0 ? (player.kills / player.deaths).toFixed(2) : player.kills}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      ) : (
        <table className="scoreboard-table">
          <thead>
            <tr>
              <th>RANK</th>
              <th>PILOT</th>
              <th>SCORE</th>
              <th>KILLS</th>
              <th>DEATHS</th>
              <th>K/D</th>
              <th>SHIP</th>
            </tr>
          </thead>
          <tbody>
            {sortedPlayers.map((player, index) => (
              <tr key={player.id}>
                <td>{index + 1}</td>
                <td>{player.name}</td>
                <td>{player.score}</td>
                <td>{player.kills}</td>
                <td>{player.deaths}</td>
                <td>{player.deaths > 0 ? (player.kills / player.deaths).toFixed(2) : player.kills}</td>
                <td>{player.ship_type}</td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
    </div>
  );
}
