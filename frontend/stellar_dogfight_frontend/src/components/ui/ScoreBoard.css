.scoreboard {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(0, 162, 255, 0.5);
  border-radius: 10px;
  padding: 20px;
  width: 80%;
  max-width: 900px;
  max-height: 80vh;
  overflow-y: auto;
  color: white;
  box-shadow: 0 0 20px rgba(0, 162, 255, 0.3);
  pointer-events: auto;
}

.scoreboard-header {
  text-align: center;
  margin-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 10px;
}

.scoreboard-header h2 {
  margin: 0;
  font-size: 1.8rem;
  color: #00a2ff;
  text-shadow: 0 0 5px rgba(0, 162, 255, 0.7);
}

.scoreboard-hint {
  margin: 5px 0 0;
  font-size: 0.8rem;
  opacity: 0.7;
}

.scoreboard-table {
  width: 100%;
  border-collapse: collapse;
}

.scoreboard-table th, .scoreboard-table td {
  padding: 10px;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.scoreboard-table th {
  background-color: rgba(0, 162, 255, 0.2);
  font-weight: bold;
  color: #00a2ff;
}

.scoreboard-table tr:nth-child(even) {
  background-color: rgba(255, 255, 255, 0.05);
}

.scoreboard-table tr:hover {
  background-color: rgba(0, 162, 255, 0.1);
}

/* Team-based styling */
.scoreboard-teams {
  display: flex;
  gap: 20px;
  justify-content: space-between;
}

.scoreboard-team {
  flex: 1;
  border-radius: 5px;
  overflow: hidden;
}

.scoreboard-team.alpha {
  border: 1px solid rgba(255, 0, 0, 0.3);
}

.scoreboard-team.omega {
  border: 1px solid rgba(0, 0, 255, 0.3);
}

.team-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
}

.scoreboard-team.alpha .team-header {
  background-color: rgba(255, 0, 0, 0.2);
}

.scoreboard-team.omega .team-header {
  background-color: rgba(0, 0, 255, 0.2);
}

.team-header h3 {
  margin: 0;
  font-size: 1.2rem;
}

.scoreboard-team.alpha h3 {
  color: #ff5555;
}

.scoreboard-team.omega h3 {
  color: #5555ff;
}

.team-score {
  font-size: 1.5rem;
  font-weight: bold;
}

.scoreboard-team.alpha .team-score {
  color: #ff5555;
}

.scoreboard-team.omega .team-score {
  color: #5555ff;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .scoreboard {
    width: 95%;
    padding: 10px;
  }
  
  .scoreboard-teams {
    flex-direction: column;
  }
  
  .scoreboard-table th, .scoreboard-table td {
    padding: 5px;
    font-size: 0.9rem;
  }
  
  .scoreboard-header h2 {
    font-size: 1.5rem;
  }
}
