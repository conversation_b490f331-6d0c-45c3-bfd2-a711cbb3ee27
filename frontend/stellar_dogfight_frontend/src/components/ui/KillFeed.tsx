import React from 'react';
import { KillFeedItem } from '../multiplayer/MultiplayerConnection';
import './KillFeed.css';

interface KillFeedProps {
  killFeed: KillFeedItem[];
}

export function KillFeed({ killFeed }: KillFeedProps) {
  return (
    <div className="kill-feed">
      {killFeed.map((item, index) => (
        <div key={index} className="kill-feed-item">
          <span className="killer-name">{item.killer.name}</span>
          <span className="kill-icon">
            {item.weapon_type === 'primary' ? '🔫' : '🚀'}
          </span>
          <span className="victim-name">{item.victim.name}</span>
        </div>
      ))}
    </div>
  );
}
