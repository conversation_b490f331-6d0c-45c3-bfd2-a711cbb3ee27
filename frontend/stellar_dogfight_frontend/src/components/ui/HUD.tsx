import React from 'react';
import './HUD.css';

interface HUDProps {
  health: number;
  energy: number;
  score: number;
  kills: number;
  deaths: number;
  shipType: 'interceptor' | 'bomber' | 'strikeFighter';
}

export function HUD({ health, energy, score, kills, deaths, shipType }: HUDProps) {
  // Ship-specific colors and icons
  const shipInfo = {
    interceptor: {
      color: '#00a2ff',
      icon: '⚡',
      name: 'INTERCEPTOR'
    },
    bomber: {
      color: '#ff3300',
      icon: '💣',
      name: 'BOMBER'
    },
    strikeFighter: {
      color: '#66ff00',
      icon: '⚔️',
      name: 'STRIKE FIGHTER'
    }
  };
  
  const currentShip = shipInfo[shipType];
  
  return (
    <div className="hud-container">
      {/* Top bar with score and ship info */}
      <div className="hud-top-bar">
        <div className="hud-score-container">
          <div className="hud-score">{score}</div>
          <div className="hud-score-label">SCORE</div>
        </div>
        
        <div className="hud-ship-info" style={{ color: currentShip.color }}>
          <div className="hud-ship-icon">{currentShip.icon}</div>
          <div className="hud-ship-name">{currentShip.name}</div>
        </div>
        
        <div className="hud-kd-ratio">
          <div className="hud-kd-stats">
            <span className="hud-kills">{kills}</span>
            <span className="hud-kd-separator">/</span>
            <span className="hud-deaths">{deaths}</span>
          </div>
          <div className="hud-kd-label">KILLS/DEATHS</div>
        </div>
      </div>
      
      {/* Bottom bar with health and energy */}
      <div className="hud-bottom-bar">
        {/* Health bar */}
        <div className="hud-stat-container">
          <div className="hud-stat-label">HULL</div>
          <div className="hud-bar-container">
            <div 
              className="hud-health-bar" 
              style={{ 
                width: `${health}%`,
                backgroundColor: health > 60 ? '#00ff00' : health > 30 ? '#ffaa00' : '#ff0000'
              }}
            ></div>
          </div>
          <div className="hud-stat-value">{health}%</div>
        </div>
        
        {/* Energy bar */}
        <div className="hud-stat-container">
          <div className="hud-stat-label">ENERGY</div>
          <div className="hud-bar-container">
            <div 
              className="hud-energy-bar" 
              style={{ 
                width: `${energy}%`,
                backgroundColor: currentShip.color
              }}
            ></div>
          </div>
          <div className="hud-stat-value">{energy}%</div>
        </div>
      </div>
      
      {/* Weapon indicators */}
      <div className="hud-weapons">
        <div className="hud-weapon primary">
          <div className="hud-weapon-label">PRIMARY</div>
          <div className="hud-weapon-name">LASER CANNON</div>
          <div className="hud-weapon-key">LMB</div>
        </div>
        
        <div className="hud-weapon secondary">
          <div className="hud-weapon-label">SECONDARY</div>
          <div className="hud-weapon-name">GUIDED MISSILE</div>
          <div className="hud-weapon-key">RMB</div>
        </div>
      </div>
      
      {/* Ability indicators */}
      <div className="hud-abilities">
        <div className="hud-ability">
          <div className="hud-ability-key">SHIFT</div>
          <div className="hud-ability-name">BOOST</div>
        </div>
        
        <div className="hud-ability">
          <div className="hud-ability-key">SPACE</div>
          <div className="hud-ability-name">BARREL ROLL</div>
        </div>
      </div>
      
      {/* Targeting reticle */}
      <div className="hud-reticle">
        <div className="hud-reticle-inner"></div>
        <div className="hud-reticle-lines">
          <div className="hud-reticle-line horizontal"></div>
          <div className="hud-reticle-line vertical"></div>
        </div>
      </div>
      
      {/* Low health warning */}
      {health <= 30 && (
        <div className={`hud-warning ${health <= 15 ? 'critical' : ''}`}>
          WARNING: HULL INTEGRITY CRITICAL
        </div>
      )}
    </div>
  );
}
