import React, { useState, useEffect } from 'react';
import { useAudioManager, AudioCategory } from '../audio/AudioManager';
import './AudioSettings.css';

interface AudioSettingsProps {
  visible: boolean;
  onClose: () => void;
}

export function AudioSettings({ visible, onClose }: AudioSettingsProps) {
  const { volumes, setVolume, playSound } = useAudioManager();
  
  // Local state for volume sliders
  const [musicVolume, setMusicVolume] = useState(volumes[AudioCategory.MUSIC]);
  const [sfxVolume, setSfxVolume] = useState(volumes[AudioCategory.SFX]);
  const [uiVolume, setUiVolume] = useState(volumes[AudioCategory.UI]);
  
  // Update local state when volumes change
  useEffect(() => {
    setMusicVolume(volumes[AudioCategory.MUSIC]);
    setSfxVolume(volumes[AudioCategory.SFX]);
    setUiVolume(volumes[AudioCategory.UI]);
  }, [volumes]);
  
  // Handle music volume change
  const handleMusicVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value);
    setMusicVolume(value);
    setVolume(AudioCategory.MUSIC, value);
  };
  
  // Handle SFX volume change
  const handleSfxVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value);
    setSfxVolume(value);
    setVolume(AudioCategory.SFX, value);
    
    // Play a sample sound
    playSound('laser_fire');
  };
  
  // Handle UI volume change
  const handleUiVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value);
    setUiVolume(value);
    setVolume(AudioCategory.UI, value);
    
    // Play a sample sound
    playSound('ui_button');
  };
  
  if (!visible) return null;
  
  return (
    <div className="audio-settings-overlay">
      <div className="audio-settings-panel">
        <h2>Audio Settings</h2>
        
        <div className="audio-setting">
          <label htmlFor="music-volume">Music Volume</label>
          <input
            id="music-volume"
            type="range"
            min="0"
            max="1"
            step="0.01"
            value={musicVolume}
            onChange={handleMusicVolumeChange}
          />
          <span className="volume-value">{Math.round(musicVolume * 100)}%</span>
        </div>
        
        <div className="audio-setting">
          <label htmlFor="sfx-volume">Sound Effects Volume</label>
          <input
            id="sfx-volume"
            type="range"
            min="0"
            max="1"
            step="0.01"
            value={sfxVolume}
            onChange={handleSfxVolumeChange}
          />
          <span className="volume-value">{Math.round(sfxVolume * 100)}%</span>
        </div>
        
        <div className="audio-setting">
          <label htmlFor="ui-volume">UI Sounds Volume</label>
          <input
            id="ui-volume"
            type="range"
            min="0"
            max="1"
            step="0.01"
            value={uiVolume}
            onChange={handleUiVolumeChange}
          />
          <span className="volume-value">{Math.round(uiVolume * 100)}%</span>
        </div>
        
        <button className="close-button" onClick={onClose}>
          Save & Close
        </button>
      </div>
    </div>
  );
}
