.audio-settings-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  pointer-events: auto;
}

.audio-settings-panel {
  background-color: rgba(13, 20, 40, 0.9);
  border-radius: 10px;
  padding: 30px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 0 30px rgba(0, 162, 255, 0.5);
  border: 1px solid rgba(0, 162, 255, 0.3);
  color: white;
}

.audio-settings-panel h2 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #00a2ff;
  text-align: center;
  font-size: 1.8rem;
  text-shadow: 0 0 10px rgba(0, 162, 255, 0.5);
}

.audio-setting {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.audio-setting label {
  width: 150px;
  font-weight: bold;
  margin-right: 15px;
}

.audio-setting input[type="range"] {
  flex: 1;
  height: 8px;
  background: rgba(0, 162, 255, 0.2);
  border-radius: 4px;
  outline: none;
  -webkit-appearance: none;
}

.audio-setting input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 20px;
  height: 20px;
  background: #00a2ff;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 0 5px rgba(0, 162, 255, 0.7);
}

.audio-setting input[type="range"]::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: #00a2ff;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 0 5px rgba(0, 162, 255, 0.7);
  border: none;
}

.volume-value {
  width: 50px;
  text-align: right;
  margin-left: 10px;
}

.close-button {
  display: block;
  width: 100%;
  padding: 12px;
  margin-top: 20px;
  background: linear-gradient(135deg, #0066cc, #00a2ff);
  border: none;
  border-radius: 5px;
  color: white;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-button:hover {
  background: linear-gradient(135deg, #00a2ff, #0066cc);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 162, 255, 0.4);
}

@media (max-width: 600px) {
  .audio-settings-panel {
    padding: 20px;
  }
  
  .audio-setting {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .audio-setting label {
    width: 100%;
    margin-bottom: 5px;
  }
  
  .audio-setting input[type="range"] {
    width: 100%;
    margin-bottom: 5px;
  }
  
  .volume-value {
    width: 100%;
    text-align: left;
    margin-left: 0;
  }
}
