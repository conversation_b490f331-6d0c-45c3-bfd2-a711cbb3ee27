.kill-feed {
  position: absolute;
  top: 100px;
  right: 40px;
  width: 300px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  pointer-events: none;
}

.kill-feed-item {
  background-color: rgba(0, 0, 0, 0.6);
  padding: 8px 12px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  animation: fade-in-out 5s forwards;
  border-left: 3px solid #00a2ff;
}

.killer-name {
  color: #00ff00;
  font-weight: bold;
  margin-right: 8px;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.kill-icon {
  margin: 0 8px;
}

.victim-name {
  color: #ff0000;
  font-weight: bold;
  margin-left: 8px;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@keyframes fade-in-out {
  0% { opacity: 0; transform: translateX(20px); }
  10% { opacity: 1; transform: translateX(0); }
  90% { opacity: 1; transform: translateX(0); }
  100% { opacity: 0; transform: translateX(20px); }
}

@media (max-width: 768px) {
  .kill-feed {
    width: 250px;
    right: 20px;
  }
  
  .kill-feed-item {
    padding: 6px 10px;
  }
  
  .killer-name, .victim-name {
    max-width: 100px;
  }
}
