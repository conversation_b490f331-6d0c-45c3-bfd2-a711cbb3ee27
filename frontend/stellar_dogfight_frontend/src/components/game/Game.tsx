import React, { useState, useEffect, useRef } from 'react';
import { Canvas } from '@react-three/fiber';
import { Stats, OrbitControls } from '@react-three/drei';
import { Object3D, Vector3, Euler } from 'three';

// Import components
import { Interceptor, Bomber, StrikeFighter } from '../spacecraft/SpacecraftModels';
import { FlightControls } from '../spacecraft/FlightControls';
import { SpaceEnvironment } from '../environment/SpaceEnvironment';
import { Projectile, Shield, Explosion } from '../effects/VisualEffects';
import { useMultiplayerConnection, Player, Projectile as ProjectileType } from '../multiplayer/MultiplayerConnection';

// Game UI components
import { HUD } from '../ui/HUD';
import { KillFeed } from '../ui/KillFeed';
import { ScoreBoard } from '../ui/ScoreBoard';

// Game modes
type GameMode = 'free-for-all' | 'team-deathmatch';

interface GameProps {
  playerName: string;
  shipType: 'interceptor' | 'bomber' | 'strikeFighter';
  gameMode: GameMode;
  onExit: () => void;
}

export function Game({ playerName, shipType, gameMode, onExit }: GameProps) {
  // Player ship reference
  const shipRef = useRef<Object3D>(null);
  
  // Game state
  const [health, setHealth] = useState(100);
  const [energy, setEnergy] = useState(100);
  const [score, setScore] = useState(0);
  const [kills, setKills] = useState(0);
  const [deaths, setDeaths] = useState(0);
  
  // Local projectiles (for visual effects before server confirmation)
  const [localProjectiles, setLocalProjectiles] = useState<{
    id: string;
    type: 'primary' | 'secondary';
    position: Vector3;
    direction: Vector3;
    timestamp: number;
  }[]>([]);
  
  // Explosions for visual effects
  const [explosions, setExplosions] = useState<{
    id: string;
    position: Vector3;
    scale: number;
    timestamp: number;
  }[]>([]);
  
  // Multiplayer connection
  const {
    connect,
    registerPlayer,
    joinRoom,
    updatePosition,
    fireWeapon,
    useAbility,
    reportHit,
    connected,
    playerId,
    room,
    players,
    projectiles,
    killFeed,
    error
  } = useMultiplayerConnection('http://localhost:5000');
  
  // Connect to server on component mount
  useEffect(() => {
    connect();
  }, []);
  
  // Register player when connected
  useEffect(() => {
    if (connected) {
      registerPlayer(playerName);
    }
  }, [connected, playerName]);
  
  // Join room when registered
  useEffect(() => {
    if (playerId) {
      joinRoom(gameMode, shipType);
    }
  }, [playerId, gameMode, shipType]);
  
  // Update local player stats from server data
  useEffect(() => {
    if (playerId && players[playerId]) {
      const player = players[playerId];
      setHealth(player.health);
      setEnergy(player.energy);
      setScore(player.score);
      setKills(player.kills);
      setDeaths(player.deaths);
    }
  }, [playerId, players]);
  
  // Handle position updates
  const handlePositionUpdate = (position: Vector3, rotation: Euler, velocity: Vector3) => {
    updatePosition(position, rotation, velocity);
  };
  
  // Handle energy changes
  const handleEnergyChange = (newEnergy: number) => {
    setEnergy(newEnergy);
  };
  
  // Handle weapon firing
  const handleWeaponFire = (weaponType: 'primary' | 'secondary', origin: Vector3, direction: Vector3) => {
    // Send to server
    fireWeapon(weaponType, origin, direction);
    
    // Add local projectile for immediate visual feedback
    const projectileId = `local_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    setLocalProjectiles(prev => [
      ...prev,
      {
        id: projectileId,
        type: weaponType,
        position: origin.clone(),
        direction: direction.clone(),
        timestamp: Date.now()
      }
    ]);
    
    // Remove local projectile after a time
    setTimeout(() => {
      setLocalProjectiles(prev => prev.filter(p => p.id !== projectileId));
    }, weaponType === 'primary' ? 2000 : 5000);
  };
  
  // Handle ability use
  const handleAbilityUse = (abilityType: 'boost' | 'barrel_roll') => {
    useAbility(abilityType);
  };
  
  // Create explosion effect
  const createExplosion = (position: Vector3, scale: number = 1) => {
    const explosionId = `explosion_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    setExplosions(prev => [
      ...prev,
      {
        id: explosionId,
        position: position.clone(),
        scale,
        timestamp: Date.now()
      }
    ]);
    
    // Remove explosion after animation completes
    setTimeout(() => {
      setExplosions(prev => prev.filter(e => e.id !== explosionId));
    }, 2000);
  };
  
  // Clean up old explosions
  useEffect(() => {
    const now = Date.now();
    setExplosions(prev => prev.filter(e => now - e.timestamp < 2000));
  }, [explosions]);
  
  // Clean up old local projectiles
  useEffect(() => {
    const now = Date.now();
    setLocalProjectiles(prev => prev.filter(p => {
      const age = now - p.timestamp;
      return p.type === 'primary' ? age < 2000 : age < 5000;
    }));
  }, [localProjectiles]);
  
  // Render player's ship based on selected type
  const renderPlayerShip = () => {
    switch (shipType) {
      case 'interceptor':
        return <Interceptor position={[0, 0, 0]} rotation={[0, 0, 0]} isPlayer={true} />;
      case 'bomber':
        return <Bomber position={[0, 0, 0]} rotation={[0, 0, 0]} isPlayer={true} />;
      case 'strikeFighter':
        return <StrikeFighter position={[0, 0, 0]} rotation={[0, 0, 0]} isPlayer={true} />;
    }
  };
  
  // Render other players' ships
  const renderOtherPlayers = () => {
    if (!playerId) return null;
    
    return Object.values(players)
      .filter(player => player.id !== playerId)
      .map(player => {
        const position: [number, number, number] = [
          player.position.x,
          player.position.y,
          player.position.z
        ];
        
        const rotation: [number, number, number] = [
          player.rotation.x,
          player.rotation.y,
          player.rotation.z
        ];
        
        switch (player.ship_type) {
          case 'interceptor':
            return (
              <group key={player.id}>
                <Interceptor position={position} rotation={rotation} />
                {player.health < 100 && (
                  <Shield 
                    position={position} 
                    radius={2} 
                    strength={player.health / 100} 
                    color={player.team === 'alpha' ? '#ff0000' : player.team === 'omega' ? '#0000ff' : '#00ff00'} 
                  />
                )}
              </group>
            );
          case 'bomber':
            return (
              <group key={player.id}>
                <Bomber position={position} rotation={rotation} />
                {player.health < 100 && (
                  <Shield 
                    position={position} 
                    radius={3} 
                    strength={player.health / 100} 
                    color={player.team === 'alpha' ? '#ff0000' : player.team === 'omega' ? '#0000ff' : '#00ff00'} 
                  />
                )}
              </group>
            );
          case 'strikeFighter':
            return (
              <group key={player.id}>
                <StrikeFighter position={position} rotation={rotation} />
                {player.health < 100 && (
                  <Shield 
                    position={position} 
                    radius={2.5} 
                    strength={player.health / 100} 
                    color={player.team === 'alpha' ? '#ff0000' : player.team === 'omega' ? '#0000ff' : '#00ff00'} 
                  />
                )}
              </group>
            );
          default:
            return null;
        }
      });
  };
  
  // Render projectiles
  const renderProjectiles = () => {
    // Combine server projectiles and local projectiles for rendering
    const allProjectiles = [
      ...projectiles.map(p => ({
        id: p.id,
        type: p.weapon_type,
        position: new Vector3(p.origin.x, p.origin.y, p.origin.z),
        direction: new Vector3(p.direction.x, p.direction.y, p.direction.z)
      })),
      ...localProjectiles
    ];
    
    return allProjectiles.map(projectile => (
      <Projectile
        key={projectile.id}
        type={projectile.type}
        position={[projectile.position.x, projectile.position.y, projectile.position.z]}
        direction={[projectile.direction.x, projectile.direction.y, projectile.direction.z]}
        speed={projectile.type === 'primary' ? 50 : 30}
        color={projectile.type === 'primary' ? '#ff0000' : '#ffaa00'}
      />
    ));
  };
  
  // Render explosions
  const renderExplosions = () => {
    return explosions.map(explosion => (
      <Explosion
        key={explosion.id}
        position={[explosion.position.x, explosion.position.y, explosion.position.z]}
        scale={explosion.scale}
      />
    ));
  };
  
  return (
    <div style={{ width: '100vw', height: '100vh', position: 'relative' }}>
      <Canvas shadows>
        {/* Performance stats (development only) */}
        <Stats />
        
        {/* Space environment */}
        <SpaceEnvironment />
        
        {/* Player ship */}
        <group ref={shipRef}>
          {renderPlayerShip()}
          {health < 100 && (
            <Shield 
              position={[0, 0, 0]} 
              radius={shipType === 'bomber' ? 3 : shipType === 'strikeFighter' ? 2.5 : 2} 
              strength={health / 100} 
            />
          )}
        </group>
        
        {/* Flight controls */}
        <FlightControls
          shipRef={shipRef}
          shipType={shipType}
          onPositionUpdate={handlePositionUpdate}
          onEnergyChange={handleEnergyChange}
          onWeaponFire={handleWeaponFire}
          onAbilityUse={handleAbilityUse}
        />
        
        {/* Other players */}
        {renderOtherPlayers()}
        
        {/* Projectiles */}
        {renderProjectiles()}
        
        {/* Explosions */}
        {renderExplosions()}
        
        {/* Lighting */}
        <ambientLight intensity={0.2} />
        <directionalLight position={[10, 10, 5]} intensity={1} castShadow />
      </Canvas>
      
      {/* Game UI */}
      <HUD
        health={health}
        energy={energy}
        score={score}
        kills={kills}
        deaths={deaths}
        shipType={shipType}
      />
      
      <KillFeed killFeed={killFeed} />
      
      <ScoreBoard
        players={Object.values(players)}
        gameMode={gameMode}
      />
      
      {/* Connection status */}
      {!connected && (
        <div className="connection-status">
          Connecting to server...
          {error && <div className="error">{error}</div>}
        </div>
      )}
      
      {/* Exit button */}
      <button className="exit-button" onClick={onExit}>
        Exit Game
      </button>
    </div>
  );
}
