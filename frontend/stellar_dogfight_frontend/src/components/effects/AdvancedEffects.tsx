import React, { useRef, useEffect } from 'react';
import { useFrame } from '@react-three/fiber';
import { Vector3, Mesh, MeshStandardMaterial, Color } from 'three';

// Advanced visual effects for combat and environment
interface CameraShakeProps {
  intensity?: number;
  decay?: number;
  maxOffset?: number;
  enabled?: boolean;
}

// Camera shake effect for impacts and explosions
export function CameraShake({ 
  intensity = 1.0, 
  decay = 0.95, 
  maxOffset = 0.3,
  enabled = true 
}: CameraShakeProps) {
  const shakeRef = useRef({
    intensity: 0,
    offset: new Vector3(0, 0, 0)
  });
  
  // Add shake impulse
  const addShake = (amount: number) => {
    if (!enabled) return;
    shakeRef.current.intensity += amount;
  };
  
  // Expose the addShake method
  React.useImperativeHandle(
    React.createRef(),
    () => ({
      addShake
    })
  );
  
  // Apply camera shake on each frame
  useFrame((state) => {
    if (!enabled || shakeRef.current.intensity <= 0.001) {
      shakeRef.current.intensity = 0;
      return;
    }
    
    // Calculate random offset based on intensity
    const offset = new Vector3(
      (Math.random() * 2 - 1) * shakeRef.current.intensity * maxOffset,
      (Math.random() * 2 - 1) * shakeRef.current.intensity * maxOffset,
      (Math.random() * 2 - 1) * shakeRef.current.intensity * maxOffset
    );
    
    // Apply to camera
    state.camera.position.add(offset.sub(shakeRef.current.offset));
    shakeRef.current.offset.copy(offset);
    
    // Decay intensity
    shakeRef.current.intensity *= decay;
  });
  
  return null;
}

// Damage effect for ships
interface DamageEffectProps {
  meshRef: React.RefObject<Mesh>;
  health: number;
  maxHealth?: number;
}

export function DamageEffect({ meshRef, health, maxHealth = 100 }: DamageEffectProps) {
  const originalMaterials = useRef<MeshStandardMaterial[]>([]);
  const damageLevel = useRef(0);
  
  // Store original materials on mount
  useEffect(() => {
    if (!meshRef.current) return;
    
    // Save original materials
    meshRef.current.traverse((child) => {
      if (child instanceof Mesh && child.material instanceof MeshStandardMaterial) {
        originalMaterials.current.push(child.material.clone());
      }
    });
  }, [meshRef]);
  
  // Update damage effects based on health
  useFrame(() => {
    if (!meshRef.current) return;
    
    const healthPercent = health / maxHealth;
    const newDamageLevel = 1 - healthPercent;
    
    // Only update if damage level changed significantly
    if (Math.abs(newDamageLevel - damageLevel.current) > 0.05) {
      damageLevel.current = newDamageLevel;
      
      // Apply damage effects to materials
      let materialIndex = 0;
      meshRef.current.traverse((child) => {
        if (child instanceof Mesh && child.material instanceof MeshStandardMaterial) {
          const originalMaterial = originalMaterials.current[materialIndex];
          
          if (originalMaterial) {
            // Darken and add burn marks as damage increases
            const burnColor = new Color(0x000000);
            const originalColor = new Color(originalMaterial.color);
            
            child.material.color.copy(originalColor).lerp(burnColor, damageLevel.current * 0.7);
            child.material.roughness = originalMaterial.roughness + damageLevel.current * 0.3;
            child.material.emissive.setRGB(damageLevel.current * 0.3, 0, 0);
            
            materialIndex++;
          }
        }
      });
    }
  });
  
  return null;
}

// Engine thruster effect
interface ThrusterEffectProps {
  position: [number, number, number];
  direction: [number, number, number];
  color?: string;
  size?: number;
  intensity?: number;
  active?: boolean;
}

export function ThrusterEffect({
  position,
  direction,
  color = '#00aaff',
  size = 1,
  intensity = 1,
  active = true
}: ThrusterEffectProps) {
  const coneRef = useRef<Mesh>(null);
  const glowRef = useRef<Mesh>(null);
  
  // Animate thruster effect
  useFrame((state) => {
    if (!coneRef.current || !glowRef.current) return;
    
    if (active) {
      // Pulsating effect
      const pulse = 0.8 + Math.sin(state.clock.elapsedTime * 10) * 0.2;
      
      // Apply to materials
      if (coneRef.current.material instanceof MeshStandardMaterial) {
        coneRef.current.material.emissiveIntensity = intensity * pulse;
        coneRef.current.scale.set(size, size, size * (0.9 + pulse * 0.2));
      }
      
      if (glowRef.current.material instanceof MeshStandardMaterial) {
        glowRef.current.material.emissiveIntensity = intensity * pulse * 1.5;
      }
    } else {
      // Inactive state
      if (coneRef.current.material instanceof MeshStandardMaterial) {
        coneRef.current.material.emissiveIntensity = 0.1;
        coneRef.current.scale.set(size * 0.5, size * 0.5, size * 0.3);
      }
      
      if (glowRef.current.material instanceof MeshStandardMaterial) {
        glowRef.current.material.emissiveIntensity = 0.2;
      }
    }
  });
  
  return (
    <group position={position} rotation={[direction[0], direction[1], direction[2]]}>
      {/* Thruster cone */}
      <mesh ref={coneRef} position={[0, 0, 0]}>
        <coneGeometry args={[0.3 * size, 1 * size, 16]} />
        <meshStandardMaterial 
          color={color} 
          emissive={color}
          emissiveIntensity={intensity}
          transparent={true}
          opacity={0.8}
          toneMapped={false}
        />
      </mesh>
      
      {/* Glow effect */}
      <mesh ref={glowRef} position={[0, 0, -0.2 * size]}>
        <sphereGeometry args={[0.2 * size, 16, 16]} />
        <meshStandardMaterial 
          color={color} 
          emissive={color}
          emissiveIntensity={intensity * 1.5}
          transparent={true}
          opacity={0.7}
          toneMapped={false}
        />
      </mesh>
      
      {/* Light source */}
      <pointLight 
        color={color} 
        intensity={intensity * 2} 
        distance={5 * size} 
        decay={2}
      />
    </group>
  );
}

// Space distortion effect (for black holes, warp, etc.)
interface SpaceDistortionProps {
  position: [number, number, number];
  radius: number;
  intensity: number;
}

export function SpaceDistortion({ position, radius, intensity }: SpaceDistortionProps) {
  const distortionRef = useRef<Mesh>(null);
  
  // Animate distortion effect
  useFrame((state) => {
    if (!distortionRef.current) return;
    
    // Pulsating effect
    const pulse = 1 + Math.sin(state.clock.elapsedTime * 0.5) * 0.1;
    
    distortionRef.current.scale.set(
      radius * pulse,
      radius * pulse,
      radius * pulse
    );
    
    if (distortionRef.current.material instanceof MeshStandardMaterial) {
      distortionRef.current.material.opacity = 0.2 + intensity * 0.3 * pulse;
    }
  });
  
  return (
    <mesh ref={distortionRef} position={position}>
      <sphereGeometry args={[1, 32, 32]} />
      <meshStandardMaterial 
        color="#000033"
        emissive="#000066"
        transparent={true}
        opacity={0.3}
        side={2} // Double-sided
        depthWrite={false}
      />
    </mesh>
  );
}

// Weapon impact effect
interface ImpactEffectProps {
  position: [number, number, number];
  normal: [number, number, number];
  color?: string;
  size?: number;
  duration?: number;
}

export function ImpactEffect({
  position,
  normal,
  color = '#ff0000',
  size = 1,
  duration = 1
}: ImpactEffectProps) {
  const [active, setActive] = React.useState(true);
  const impactRef = useRef<Mesh>(null);
  const timeRef = useRef(0);
  
  // Animate impact effect
  useFrame((state, delta) => {
    if (!impactRef.current || !active) return;
    
    timeRef.current += delta;
    
    // Scale up quickly then fade out
    const progress = timeRef.current / duration;
    const scale = size * (progress < 0.2 ? progress / 0.2 : 1 - (progress - 0.2) / 0.8);
    
    impactRef.current.scale.set(scale, scale, scale);
    
    if (impactRef.current.material instanceof MeshStandardMaterial) {
      impactRef.current.material.opacity = 1 - progress;
    }
    
    // Remove when animation completes
    if (progress >= 1) {
      setActive(false);
    }
  });
  
  if (!active) return null;
  
  return (
    <mesh ref={impactRef} position={position} rotation={[normal[0], normal[1], normal[2]]}>
      <planeGeometry args={[1, 1]} />
      <meshStandardMaterial 
        color={color} 
        emissive={color}
        emissiveIntensity={2}
        transparent={true}
        opacity={1}
        toneMapped={false}
        depthWrite={false}
      />
    </mesh>
  );
}
