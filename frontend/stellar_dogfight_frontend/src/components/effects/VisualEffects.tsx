import React, { useRef, useEffect } from 'react';
import { useFrame } from '@react-three/fiber';
import { Vector3, Mesh, Group, PointLight } from 'three';

// Particle system for visual effects
interface ParticleSystemProps {
  position: [number, number, number];
  count: number;
  color: string;
  size: number;
  speed: number;
  spread: number;
  lifetime: number;
  emissionRate: number;
  type: 'explosion' | 'thruster' | 'laser' | 'missile';
}

export function ParticleSystem({
  position,
  count,
  color,
  size,
  speed,
  spread,
  lifetime,
  emissionRate,
  type
}: ParticleSystemProps) {
  const groupRef = useRef<Group>(null);
  const particles = useRef<Array<{
    position: Vector3;
    velocity: Vector3;
    age: number;
    maxAge: number;
    size: number;
    active: boolean;
  }>>([]);
  
  const lightRef = useRef<PointLight>(null);
  const emissionCounter = useRef(0);
  
  // Initialize particle system
  useEffect(() => {
    particles.current = Array.from({ length: count }).map(() => ({
      position: new Vector3(0, 0, 0),
      velocity: new Vector3(0, 0, 0),
      age: Infinity, // Start inactive
      maxAge: 0,
      size: 0,
      active: false
    }));
  }, [count]);
  
  // Emit a new particle
  const emitParticle = () => {
    // Find an inactive particle
    const inactiveIndex = particles.current.findIndex(p => !p.active);
    if (inactiveIndex === -1) return;
    
    const particle = particles.current[inactiveIndex];
    
    // Reset position to emitter
    particle.position.set(position[0], position[1], position[2]);
    
    // Set velocity based on particle type
    switch (type) {
      case 'explosion':
        // Radial explosion
        const direction = new Vector3(
          (Math.random() - 0.5) * 2,
          (Math.random() - 0.5) * 2,
          (Math.random() - 0.5) * 2
        ).normalize();
        
        particle.velocity.copy(direction).multiplyScalar(speed * (0.5 + Math.random() * 0.5));
        particle.maxAge = lifetime * (0.7 + Math.random() * 0.6);
        particle.size = size * (0.5 + Math.random() * 0.5);
        break;
        
      case 'thruster':
        // Cone-shaped emission behind ship
        particle.velocity.set(
          (Math.random() - 0.5) * spread,
          (Math.random() - 0.5) * spread,
          -speed * (0.8 + Math.random() * 0.4)
        );
        particle.maxAge = lifetime * (0.7 + Math.random() * 0.3);
        particle.size = size * (0.5 + Math.random() * 0.5);
        break;
        
      case 'laser':
        // Straight line with slight spread
        particle.velocity.set(
          (Math.random() - 0.5) * spread * 0.2,
          (Math.random() - 0.5) * spread * 0.2,
          speed * (0.9 + Math.random() * 0.2)
        );
        particle.maxAge = lifetime * (0.8 + Math.random() * 0.4);
        particle.size = size * (0.7 + Math.random() * 0.3);
        break;
        
      case 'missile':
        // Smoke trail
        particle.velocity.set(
          (Math.random() - 0.5) * spread,
          (Math.random() - 0.5) * spread,
          (Math.random() - 0.5) * spread
        );
        particle.maxAge = lifetime * (0.5 + Math.random() * 0.5);
        particle.size = size * (0.6 + Math.random() * 0.8);
        break;
    }
    
    // Activate particle
    particle.age = 0;
    particle.active = true;
  };
  
  // Update particles on each frame
  useFrame((state, delta) => {
    if (!groupRef.current) return;
    
    // Emit new particles based on emission rate
    emissionCounter.current += delta * emissionRate;
    while (emissionCounter.current >= 1) {
      emitParticle();
      emissionCounter.current -= 1;
    }
    
    // Update light intensity based on active particles
    if (lightRef.current) {
      const activeCount = particles.current.filter(p => p.active).length;
      const intensity = activeCount / count * 5;
      lightRef.current.intensity = intensity;
    }
    
    // Update each particle
    particles.current.forEach((particle, i) => {
      if (!particle.active) return;
      
      // Update age
      particle.age += delta;
      
      // Deactivate if too old
      if (particle.age >= particle.maxAge) {
        particle.active = false;
        return;
      }
      
      // Update position
      particle.position.add(particle.velocity.clone().multiplyScalar(delta));
      
      // Apply any effects based on particle type
      switch (type) {
        case 'explosion':
          // Slow down over time
          particle.velocity.multiplyScalar(0.95);
          break;
          
        case 'thruster':
          // Expand slightly
          particle.size *= 1.01;
          break;
          
        case 'missile':
          // Expand smoke
          particle.size *= 1.02;
          break;
      }
      
      // Update mesh
      const child = groupRef.current?.children[i] as Mesh;
      if (child) {
        child.position.copy(particle.position);
        
        // Scale based on lifetime (fade in/out)
        const lifeRatio = particle.age / particle.maxAge;
        let scale = particle.size;
        
        // Different scale curves based on type
        if (type === 'explosion') {
          // Expand then contract
          scale *= lifeRatio < 0.3 ? lifeRatio / 0.3 : 1 - (lifeRatio - 0.3) / 0.7;
        } else if (type === 'laser') {
          // Maintain size then fade
          scale *= lifeRatio < 0.8 ? 1 : 1 - (lifeRatio - 0.8) / 0.2;
        } else {
          // Fade out at end
          scale *= lifeRatio < 0.7 ? 1 : 1 - (lifeRatio - 0.7) / 0.3;
        }
        
        child.scale.set(scale, scale, scale);
        
        // Set visibility
        child.visible = particle.active;
      }
    });
  });
  
  return (
    <group>
      <group ref={groupRef}>
        {Array.from({ length: count }).map((_, i) => (
          <mesh key={i} visible={false}>
            <sphereGeometry args={[1, 8, 8]} />
            <meshBasicMaterial 
              color={color} 
              transparent={true} 
              opacity={0.7}
              toneMapped={false}
            />
          </mesh>
        ))}
      </group>
      <pointLight 
        ref={lightRef} 
        color={color} 
        intensity={0} 
        distance={10} 
        decay={2}
        position={position}
      />
    </group>
  );
}

// Projectile effects (lasers and missiles)
interface ProjectileProps {
  type: 'laser' | 'missile';
  position: [number, number, number];
  direction: [number, number, number];
  speed: number;
  color: string;
  onHit?: () => void;
}

export function Projectile({
  type,
  position,
  direction,
  speed,
  color,
  onHit
}: ProjectileProps) {
  const meshRef = useRef<Mesh>(null);
  const velocity = useRef(new Vector3(direction[0], direction[1], direction[2]).normalize().multiplyScalar(speed));
  const lifetime = useRef(0);
  const maxLifetime = type === 'laser' ? 2 : 8; // Seconds
  
  useFrame((state, delta) => {
    if (!meshRef.current) return;
    
    // Update position
    meshRef.current.position.add(velocity.current.clone().multiplyScalar(delta));
    
    // Update lifetime
    lifetime.current += delta;
    if (lifetime.current >= maxLifetime) {
      // Trigger hit effect and remove
      if (onHit) onHit();
    }
    
    // For missiles, add slight homing behavior in a real implementation
  });
  
  return (
    <group position={position}>
      <mesh ref={meshRef}>
        {type === 'laser' ? (
          // Laser beam
          <>
            <cylinderGeometry args={[0.05, 0.05, 3, 8]} />
            <meshBasicMaterial color={color} toneMapped={false} />
          </>
        ) : (
          // Missile
          <>
            <cylinderGeometry args={[0.1, 0.1, 0.8, 8]} />
            <meshStandardMaterial color="#555555" metalness={0.8} roughness={0.2} />
          </>
        )}
      </mesh>
      
      {/* Particle effects */}
      {type === 'laser' ? (
        <ParticleSystem
          position={[0, 0, 0]}
          count={20}
          color={color}
          size={0.1}
          speed={1}
          spread={0.2}
          lifetime={0.5}
          emissionRate={10}
          type="laser"
        />
      ) : (
        <ParticleSystem
          position={[0, 0, -0.5]}
          count={50}
          color="#ff8800"
          size={0.2}
          speed={0.5}
          spread={0.5}
          lifetime={1}
          emissionRate={20}
          type="missile"
        />
      )}
    </group>
  );
}

// Explosion effect
interface ExplosionProps {
  position: [number, number, number];
  scale?: number;
}

export function Explosion({ position, scale = 1 }: ExplosionProps) {
  const [active, setActive] = React.useState(true);
  const lifetime = useRef(0);
  const maxLifetime = 2; // Seconds
  
  useFrame((state, delta) => {
    lifetime.current += delta;
    if (lifetime.current >= maxLifetime) {
      setActive(false);
    }
  });
  
  if (!active) return null;
  
  return (
    <group position={position} scale={[scale, scale, scale]}>
      <ParticleSystem
        position={[0, 0, 0]}
        count={100}
        color="#ff5500"
        size={0.5}
        speed={5}
        spread={1}
        lifetime={1.5}
        emissionRate={50}
        type="explosion"
      />
      <pointLight color="#ff5500" intensity={10} distance={20} decay={2} />
    </group>
  );
}

// Shield effect
interface ShieldProps {
  position: [number, number, number];
  radius: number;
  strength: number; // 0-1
  color?: string;
}

export function Shield({ position, radius, strength, color = "#00aaff" }: ShieldProps) {
  const shieldRef = useRef<Mesh>(null);
  
  useFrame((state) => {
    if (shieldRef.current) {
      // Pulsating effect
      const scale = 1 + Math.sin(state.clock.getElapsedTime() * 5) * 0.03;
      shieldRef.current.scale.set(scale, scale, scale);
    }
  });
  
  if (strength <= 0) return null;
  
  return (
    <mesh ref={shieldRef} position={position}>
      <sphereGeometry args={[radius, 32, 32]} />
      <meshBasicMaterial
        color={color}
        transparent={true}
        opacity={0.2 + strength * 0.3}
        toneMapped={false}
      />
    </mesh>
  );
}
