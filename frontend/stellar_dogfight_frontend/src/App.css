/* Space-themed styling for the entry interface */
body, html {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  font-family: 'Arial', sans-serif;
  overflow: hidden;
}

.entry-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  background-color: #000;
  overflow: hidden;
}

/* Stars background effect */
.stars {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #000 url('https://i.imgur.com/YKY28eT.png') repeat;
  z-index: 0;
}

/* Twinkling stars effect */
.twinkling {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent url('https://i.imgur.com/XYMF4ca.png') repeat;
  z-index: 1;
  animation: move-twink-back 200s linear infinite;
}

@keyframes move-twink-back {
  from {background-position: 0 0;}
  to {background-position: -10000px 5000px;}
}

.entry-card {
  position: relative;
  z-index: 2;
  background: rgba(13, 20, 40, 0.8);
  border-radius: 10px;
  padding: 40px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 0 30px rgba(0, 162, 255, 0.3);
  border: 1px solid rgba(0, 162, 255, 0.2);
  backdrop-filter: blur(10px);
  text-align: center;
}

h1 {
  margin: 0;
  font-size: 2.5rem;
  letter-spacing: 3px;
  color: #00a2ff;
  text-shadow: 0 0 10px rgba(0, 162, 255, 0.7);
}

.tagline {
  margin-top: 5px;
  margin-bottom: 30px;
  color: #aaa;
  font-style: italic;
}

.input-group {
  margin-bottom: 20px;
  text-align: left;
}

label {
  display: block;
  margin-bottom: 8px;
  color: #00a2ff;
  font-weight: bold;
}

input {
  width: 100%;
  padding: 12px;
  border: 1px solid #00a2ff;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 5px;
  color: white;
  font-size: 1rem;
  box-sizing: border-box;
}

input:focus {
  outline: none;
  box-shadow: 0 0 10px rgba(0, 162, 255, 0.5);
}

.join-button {
  width: 100%;
  padding: 15px;
  background: linear-gradient(135deg, #0066cc, #00a2ff);
  border: none;
  border-radius: 5px;
  color: white;
  font-size: 1.1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 20px;
}

.join-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #00a2ff, #0066cc);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 162, 255, 0.4);
}

.join-button:disabled {
  background: #555;
  cursor: not-allowed;
}

.join-button.joining {
  background: #555;
  position: relative;
}

.join-button.joining:after {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s infinite linear;
}

@keyframes spin {
  to { transform: translateY(-50%) rotate(360deg); }
}

.ship-selection-coming-soon {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.ship-icons {
  display: flex;
  justify-content: space-around;
  margin: 15px 0;
}

.ship-icon {
  width: 50px;
  height: 50px;
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid #00a2ff;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: #00a2ff;
  font-size: 1.5rem;
}

.ship-labels {
  display: flex;
  justify-content: space-around;
  font-size: 0.8rem;
  color: #aaa;
}

/* Loading screen styles */
.game-container {
  width: 100vw;
  height: 100vh;
  background: #000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-screen {
  text-align: center;
  color: white;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(0, 162, 255, 0.3);
  border-radius: 50%;
  border-top-color: #00a2ff;
  animation: spin 1s infinite linear;
  margin: 20px auto;
}

.hint {
  font-size: 0.8rem;
  color: #666;
  margin-top: 30px;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .entry-card {
    padding: 20px;
    width: 85%;
  }
  
  h1 {
    font-size: 2rem;
  }
}
