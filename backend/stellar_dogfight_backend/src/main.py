import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))  # DON'T CHANGE THIS !!!

from flask import Flask, request, jsonify, render_template
from flask_socketio import Socket<PERSON>, emit, join_room, leave_room
import json
import uuid
import time

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'stellar-dogfight-secret-key!'

# Initialize SocketIO with CORS support
socketio = SocketIO(app, cors_allowed_origins="*")

# In-memory game state storage (would use a database in production)
players = {}
game_rooms = {
    'free-for-all': {
        'players': {},
        'max_players': 32,
        'type': 'free-for-all'
    },
    'team-deathmatch': {
        'players': {},
        'max_players': 32,
        'type': 'team-deathmatch',
        'teams': {
            'alpha': {'players': {}, 'score': 0},
            'omega': {'players': {}, 'score': 0}
        }
    }
}

# Routes
@app.route('/')
def index():
    return jsonify({
        'status': 'online',
        'message': 'Stellar Dogfight API is running',
        'version': '0.1.0'
    })

@app.route('/api/status')
def status():
    return jsonify({
        'status': 'online',
        'players_online': len(players),
        'rooms': {
            'free-for-all': len(game_rooms['free-for-all']['players']),
            'team-deathmatch': len(game_rooms['team-deathmatch']['players'])
        }
    })

# Socket.IO event handlers
@socketio.on('connect')
def handle_connect():
    print(f'Client connected: {request.sid}')
    emit('connection_established', {'status': 'connected'})

@socketio.on('disconnect')
def handle_disconnect():
    player_id = None
    # Find player by socket id
    for pid, player_data in players.items():
        if player_data.get('sid') == request.sid:
            player_id = pid
            break
    
    if player_id:
        # Remove player from any rooms they were in
        for room_id, room_data in game_rooms.items():
            if player_id in room_data['players']:
                del room_data['players'][player_id]
                # Notify other players
                emit('player_disconnected', {'player_id': player_id}, room=room_id)
        
        # Remove player from global players list
        del players[player_id]
        print(f'Player {player_id} disconnected')

@socketio.on('register_player')
def handle_register_player(data):
    player_name = data.get('name', 'Unknown Pilot')
    player_id = str(uuid.uuid4())
    
    # Store player data
    players[player_id] = {
        'id': player_id,
        'name': player_name,
        'sid': request.sid,
        'joined_at': time.time(),
        'room': None,
        'ship_type': None,
        'position': {'x': 0, 'y': 0, 'z': 0},
        'rotation': {'x': 0, 'y': 0, 'z': 0},
        'velocity': {'x': 0, 'y': 0, 'z': 0},
        'health': 100,
        'energy': 100,
        'score': 0,
        'kills': 0,
        'deaths': 0
    }
    
    # Send confirmation to the player
    emit('registration_complete', {
        'player_id': player_id,
        'name': player_name,
        'status': 'registered'
    })
    
    print(f'Player registered: {player_name} ({player_id})')

@socketio.on('join_room')
def handle_join_room(data):
    player_id = data.get('player_id')
    room_id = data.get('room_id', 'free-for-all')
    ship_type = data.get('ship_type', 'interceptor')
    
    if player_id not in players:
        emit('error', {'message': 'Player not registered'})
        return
    
    if room_id not in game_rooms:
        emit('error', {'message': 'Invalid room'})
        return
    
    room = game_rooms[room_id]
    
    # Check if room is full
    if len(room['players']) >= room['max_players']:
        emit('error', {'message': 'Room is full'})
        return
    
    # Add player to room
    join_room(room_id)
    
    # Update player data
    players[player_id]['room'] = room_id
    players[player_id]['ship_type'] = ship_type
    
    # Randomize initial position (would be more sophisticated in production)
    import random
    players[player_id]['position'] = {
        'x': random.uniform(-500, 500),
        'y': random.uniform(-100, 100),
        'z': random.uniform(-500, 500)
    }
    
    # Add player to room's player list
    room['players'][player_id] = players[player_id]
    
    # Notify player of successful join
    emit('room_joined', {
        'room_id': room_id,
        'player_count': len(room['players']),
        'position': players[player_id]['position'],
        'players': {pid: {
            'id': p['id'],
            'name': p['name'],
            'ship_type': p['ship_type'],
            'position': p['position'],
            'rotation': p['rotation']
        } for pid, p in room['players'].items()}
    })
    
    # Notify other players in the room
    emit('player_joined', {
        'player': {
            'id': player_id,
            'name': players[player_id]['name'],
            'ship_type': ship_type,
            'position': players[player_id]['position'],
            'rotation': players[player_id]['rotation']
        }
    }, room=room_id, skip_sid=request.sid)
    
    print(f'Player {player_id} joined room {room_id} with ship type {ship_type}')

@socketio.on('position_update')
def handle_position_update(data):
    player_id = data.get('player_id')
    position = data.get('position')
    rotation = data.get('rotation')
    velocity = data.get('velocity')
    
    if not player_id or player_id not in players:
        return
    
    player = players[player_id]
    room_id = player['room']
    
    if not room_id or room_id not in game_rooms:
        return
    
    # Update player position, rotation, and velocity
    if position:
        player['position'] = position
    if rotation:
        player['rotation'] = rotation
    if velocity:
        player['velocity'] = velocity
    
    # Broadcast to other players in the room
    emit('player_position', {
        'player_id': player_id,
        'position': player['position'],
        'rotation': player['rotation'],
        'velocity': player['velocity']
    }, room=room_id, skip_sid=request.sid)

@socketio.on('weapon_fire')
def handle_weapon_fire(data):
    player_id = data.get('player_id')
    weapon_type = data.get('weapon_type', 'primary')  # primary or secondary
    projectile_id = data.get('projectile_id')
    origin = data.get('origin')
    direction = data.get('direction')
    
    if not player_id or player_id not in players:
        return
    
    player = players[player_id]
    room_id = player['room']
    
    if not room_id or room_id not in game_rooms:
        return
    
    # Broadcast weapon fire to all players in the room
    emit('projectile_fired', {
        'player_id': player_id,
        'weapon_type': weapon_type,
        'projectile_id': projectile_id,
        'origin': origin,
        'direction': direction
    }, room=room_id)

@socketio.on('hit_detection')
def handle_hit_detection(data):
    shooter_id = data.get('shooter_id')
    target_id = data.get('target_id')
    weapon_type = data.get('weapon_type', 'primary')
    damage = data.get('damage', 10)  # Default damage
    
    if not shooter_id or shooter_id not in players:
        return
    
    if not target_id or target_id not in players:
        return
    
    shooter = players[shooter_id]
    target = players[target_id]
    
    if shooter['room'] != target['room']:
        return
    
    room_id = shooter['room']
    
    # Calculate damage (would be more sophisticated in production)
    if weapon_type == 'primary':
        actual_damage = damage
    elif weapon_type == 'secondary':
        actual_damage = damage * 2  # Missiles do more damage
    
    # Apply damage to target
    target['health'] -= actual_damage
    
    # Check if target is destroyed
    if target['health'] <= 0:
        target['health'] = 0
        target['deaths'] += 1
        shooter['kills'] += 1
        shooter['score'] += 100
        
        # Respawn target after a delay (would be handled by client in production)
        target['health'] = 100
        
        # Randomize respawn position
        import random
        target['position'] = {
            'x': random.uniform(-500, 500),
            'y': random.uniform(-100, 100),
            'z': random.uniform(-500, 500)
        }
        
        # Broadcast kill feed
        emit('kill_feed', {
            'killer': {
                'id': shooter_id,
                'name': shooter['name']
            },
            'victim': {
                'id': target_id,
                'name': target['name']
            },
            'weapon_type': weapon_type
        }, room=room_id)
    
    # Broadcast hit to all players
    emit('player_hit', {
        'target_id': target_id,
        'shooter_id': shooter_id,
        'damage': actual_damage,
        'health_remaining': target['health'],
        'weapon_type': weapon_type
    }, room=room_id)

@socketio.on('ability_use')
def handle_ability_use(data):
    player_id = data.get('player_id')
    ability_type = data.get('ability_type')  # 'boost' or 'barrel_roll'
    
    if not player_id or player_id not in players:
        return
    
    player = players[player_id]
    room_id = player['room']
    
    if not room_id or room_id not in game_rooms:
        return
    
    # Energy cost for abilities
    energy_cost = 20 if ability_type == 'boost' else 15
    
    # Check if player has enough energy
    if player['energy'] < energy_cost:
        emit('ability_failed', {
            'player_id': player_id,
            'ability_type': ability_type,
            'reason': 'insufficient_energy'
        })
        return
    
    # Deduct energy
    player['energy'] -= energy_cost
    
    # Broadcast ability use to all players in the room
    emit('ability_used', {
        'player_id': player_id,
        'ability_type': ability_type,
        'energy_remaining': player['energy']
    }, room=room_id)
    
    # Energy regeneration would be handled by a separate process in production

if __name__ == '__main__':
    socketio.run(app, host='0.0.0.0', port=5000, debug=True)
