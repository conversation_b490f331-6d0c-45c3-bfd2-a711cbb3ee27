# Stellar Dogfight

A real-time multiplayer space combat game featuring modern jet fighter-inspired spacecraft, realistic flight physics, and immersive space environments.

## Game Overview

Stellar Dogfight is a WebGL-based multiplayer space combat game that allows up to 32 concurrent players to engage in fast-paced dogfights across an infinite procedural space environment. Players can choose from three distinct spacecraft classes, each with unique characteristics and abilities.

## Features

### Game Entry & Authentication
- Minimal entry interface with name input and "Join Battle" button
- Unique session ID generation for each player
- WebSocket connections for instant player synchronization

### Spacecraft Design & Controls
- Three distinct fighter classes inspired by modern jet fighters:
  * **Interceptor**: Fast and agile, inspired by F-22 Raptor
  * **Bomber**: Heavy and powerful, inspired by B-2 Spirit
  * **Strike Fighter**: Balanced multi-role fighter inspired by F/A-18
- Realistic flight physics including thrust, roll, pitch, and yaw
- Responsive controls using WASD for movement and mouse for aiming
- Combat mechanics:
  * Primary weapon: Rapid-fire laser cannons (Left click)
  * Secondary weapon: Guided missiles with lock-on capability (Right click)
  * Defensive abilities: Boost (Shift) and Barrel roll (Space)
  * Energy management system for weapons and abilities

### Visual Environment
- Procedurally infinite space environment using a coordinate system
- Dynamic celestial objects:
  * Rotating planets with unique textures and atmospheres
  * Nebulae and star clusters as visual landmarks
  * Interactive black holes affecting ship trajectory
  * Asteroid fields for cover and obstacles
- Realistic lighting effects and particle systems

### Multiplayer Features
- Support for 32 concurrent players
- Server-side hit detection and damage calculation
- Player position and action synchronization at 60Hz
- Kill feed and scoring system
- Team-based game modes (Free-for-all, Team Deathmatch)

### Technical Implementation
- WebGL for 3D rendering via Three.js and React Three Fiber
- Client-side prediction for smooth gameplay
- Optimized network traffic for low-latency combat
- Sound effects and background music for immersion
- Basic anti-cheat measures

## Project Structure

```
stellar_dogfight/
├── backend/                      # Flask backend with WebSocket server
│   └── stellar_dogfight_backend/
│       ├── src/                  # Backend source code
│       │   ├── models/           # Data models
│       │   ├── routes/           # API endpoints
│       │   ├── static/           # Static assets
│       │   └── main.py           # Main entry point
│       ├── venv/                 # Python virtual environment
│       └── requirements.txt      # Python dependencies
│
├── frontend/                     # React frontend
│   └── stellar_dogfight_frontend/
│       ├── public/               # Public assets
│       ├── src/                  # Frontend source code
│       │   ├── assets/           # Static assets
│       │   ├── components/       # React components
│       │   │   ├── audio/        # Audio management
│       │   │   ├── effects/      # Visual effects
│       │   │   ├── environment/  # Space environment
│       │   │   ├── game/         # Main game component
│       │   │   ├── multiplayer/  # Multiplayer connection
│       │   │   ├── spacecraft/   # Spacecraft models and controls
│       │   │   └── ui/           # User interface components
│       │   ├── App.tsx           # Main application component
│       │   └── main.tsx          # Entry point
│       └── package.json          # Node.js dependencies
│
├── validation.js                 # Performance validation script
└── README.md                     # Project documentation
```

## Getting Started

### Prerequisites
- Node.js 18+ and npm/pnpm
- Python 3.8+
- Modern web browser with WebGL support

### Running the Backend
1. Navigate to the backend directory:
   ```
   cd backend/stellar_dogfight_backend
   ```

2. Activate the virtual environment:
   ```
   source venv/bin/activate
   ```

3. Start the Flask server:
   ```
   python src/main.py
   ```

### Running the Frontend
1. Navigate to the frontend directory:
   ```
   cd frontend/stellar_dogfight_frontend
   ```

2. Install dependencies:
   ```
   pnpm install
   ```

3. Start the development server:
   ```
   pnpm run dev
   ```

4. Open your browser and navigate to the URL shown in the terminal (typically http://localhost:5173)

## Known Issues

- TypeScript build warnings related to Three.js type declarations
- Some unused imports in component files
- Audio files need to be placed in the public directory before production build

## Future Enhancements

- Enhanced AI opponents for single-player mode
- Additional spacecraft classes and customization options
- More diverse space environments and mission types
- Advanced matchmaking and ranking system
- Mobile-optimized controls for touch devices

## Credits

Developed by Manus AI as a demonstration of real-time multiplayer game development using modern web technologies.
